"""
Enhanced Project Assumptions Data Model
=======================================

Enhanced data model for project assumptions with validation and calculations.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import logging


@dataclass
class EnhancedProjectAssumptions:
    """Enhanced project assumptions with additional validation and methods."""
    
    # Technical parameters
    technology_type: str = "Solar PV"
    capacity_mw: float = 10.0
    production_mwh_year1: float = 18000.0
    degradation_rate: float = 0.005
    project_life_years: int = 25
    
    # Financial parameters
    capex_meur: float = 8.5
    opex_keuros_year1: float = 180.0
    ppa_price_eur_kwh: float = 0.045
    ppa_escalation: float = 0.0
    debt_ratio: float = 0.75
    interest_rate: float = 0.06
    debt_years: int = 15
    discount_rate: float = 0.08
    tax_rate: float = 0.15
    land_lease_eur_mw_year: float = 2000.0
    
    # Tax and financing parameters
    tax_holiday: int = 5
    grace_years: int = 2
    
    # Grant parameters
    # Internal canonical field for unified Piano Mattei/SIMEST grants
    _grant_meur_piano_mattei_unified: float = 0.0
    grant_meur_masen: float = 0.0  # MASEN commercial project incentives (primary for commercial projects)
    grant_meur_iresen: float = 0.0  # IRESEN R&D incentives (typically 0 for commercial projects)
    grant_meur_connection: float = 0.0  # Grid connection support grants
    grant_meur_cri: float = 0.0  # Regional investment center grants
    
    # Location parameters
    project_location: str = "Ouarzazate"
    comparable_location: Optional[str] = None  # For location comparison analysis
    
    # SIMEST program parameters
    company_location: str = "Italy"  # Company location to determine SIMEST grant percentage
    company_southern_italy: bool = False  # True for 20% grant, False for 10% grant

    # UI-specific fields
    is_validated: bool = False
    validation_errors: Dict[str, str] = field(default_factory=dict)
    last_modified: Optional[str] = None
    
    def __post_init__(self):
        """Enhanced post-initialization with validation."""
        # Initialize logger
        self.logger = logging.getLogger(__name__)
        
        # Ensure calculated fields are available for validation
        if not hasattr(self, 'investment_for_debt_sizing_meur'):
            self.investment_for_debt_sizing_meur = self.capex_meur - self.calculate_total_grants()

        self.validate_all()
    
    def validate_all(self) -> Dict[str, str]:
        """Comprehensive validation of all parameters."""
        errors = {}
        
        # Basic parameter validation
        if self.capacity_mw <= 0:
            errors['capacity_mw'] = "Capacity must be positive"
        elif self.capacity_mw > 1000:
            errors['capacity_mw'] = "Capacity seems unreasonably high (>1000 MW)"
        
        if self.project_life_years < 10 or self.project_life_years > 50:
            errors['project_life_years'] = "Project life should be between 10-50 years"
        
        if self.capex_meur <= 0:
            errors['capex_meur'] = "CAPEX must be positive"
        elif self.capex_meur / self.capacity_mw > 2.0:
            errors['capex_meur'] = "CAPEX per MW seems high (>2.0 M EUR/MW)"
        
        # Financial validation
        if not 0 < self.debt_ratio < 1:
            errors['debt_ratio'] = "Debt ratio must be between 0 and 1"
        
        if self.interest_rate < 0 or self.interest_rate > 0.2:
            errors['interest_rate'] = "Interest rate should be between 0-20%"
        
        if self.discount_rate < 0 or self.discount_rate > 0.3:
            errors['discount_rate'] = "Discount rate should be between 0-30%"
        
        if self.debt_years > self.project_life_years:
            errors['debt_years'] = "Debt tenor cannot exceed project life"
        
        # Tax and grace period validation
        if self.tax_rate < 0 or self.tax_rate > 0.5:
            errors['tax_rate'] = "Tax rate should be between 0-50%"

        if self.tax_holiday < 0 or self.tax_holiday > self.project_life_years:
            errors['tax_holiday'] = "Tax holiday period must be between 0 and project life"

        if self.grace_years < 0 or self.grace_years > self.debt_years:
            errors['grace_years'] = "Debt grace period cannot exceed debt tenor"
        
        # Revenue validation
        if self.production_mwh_year1 <= 0:
            errors['production_mwh_year1'] = "Production must be positive"
        
        capacity_factor = self.production_mwh_year1 / (self.capacity_mw * 8760)
        if capacity_factor > 0.6:
            errors['production_mwh_year1'] = "Capacity factor seems high (>60%)"
        elif capacity_factor < 0.1:
            errors['production_mwh_year1'] = "Capacity factor seems low (<10%)"
        
        if self.ppa_price_eur_kwh <= 0 or self.ppa_price_eur_kwh > 0.5:
            errors['ppa_price_eur_kwh'] = "PPA price should be between 0-0.5 EUR/kWh"
        
        # Grant validation
        total_grants = self.calculate_total_grants()
        if total_grants > self.capex_meur:
            errors['grants'] = "Total grants cannot exceed CAPEX"
        
        # CRI grant specific validation
        if self.grant_meur_cri > 0:
            # CRI grant cannot exceed 30% of CAPEX
            max_cri_grant = 0.3 * self.capex_meur
            if self.grant_meur_cri > max_cri_grant:
                errors['grant_meur_cri'] = f"CRI grant cannot exceed 30% of CAPEX (max: {max_cri_grant:.2f} M EUR)"
            
            # CRI grant is primarily for Morocco - warn if used elsewhere
            morocco_locations = ['morocco', 'maroc', 'ouarzazate', 'dakhla', 'laâyoune', 'laayoune', 'noor_midelt', 'tarfaya']
            if self.project_location.lower() not in morocco_locations:
                errors['grant_meur_cri_location'] = "CRI grant is designed for Morocco-based projects. Using CRI grant for non-Morocco projects may not be compliant with grant terms."
        
        # IRESEN grant validation (typically for R&D, not commercial projects)
        if self.grant_meur_iresen > 0:
            # Warning for commercial projects using IRESEN
            if self.grant_meur_iresen > 0.5:  # Significant IRESEN funding
                errors['grant_meur_iresen_commercial'] = "IRESEN grants are primarily for R&D and non-commercial projects. Consider using MASEN grants for commercial projects instead."
            
            # IRESEN should be much smaller than MASEN for commercial projects
            if self.grant_meur_iresen > self.grant_meur_masen and self.grant_meur_masen > 0:
                errors['grant_meur_iresen_vs_masen'] = "For commercial projects, MASEN grants should typically be larger than IRESEN grants. IRESEN is primarily for R&D."
        
        # SIMEST program validation
        if self._grant_meur_piano_mattei_unified > 0:
            # Validate SIMEST eligibility using configuration
            try:
                eligibility = self._validate_simest_eligibility()
                if not eligibility['is_eligible']:
                    errors['simest_eligibility'] = "; ".join(eligibility['errors'])
                
                # Validate SIMEST facility amounts
                facility_validation = self._validate_simest_facility_amounts()
                if facility_validation:
                    errors.update(facility_validation)
                
                # Validate grant percentage limits
                grant_validation = self._validate_simest_grant_limits()
                if grant_validation:
                    errors.update(grant_validation)
                    
            except Exception as e:
                errors['simest_config'] = f"SIMEST configuration error: {str(e)}"
        
        self.validation_errors = errors
        self.is_validated = len(errors) == 0
        
        return errors
    
    def calculate_total_grants(self) -> float:
        """Calculate total grants from all sources.

        Includes grants from:
        - SIMEST Grant Component (only the non-repayable grant portion, not the full facility)
        - MASEN (grant_meur_masen) - Primary Moroccan incentives for commercial projects
        - IRESEN (grant_meur_iresen) - R&D incentives (typically 0 for commercial projects)
        - Connection (grant_meur_connection) - Grid connection support
        - CRI (grant_meur_cri) - Regional investment support

        Returns:
            float: Total grants in MEUR
        """
        # Use only the grant component of SIMEST, not the full facility
        simest_grant_component = self.calculate_simest_grant_component()
        
        return (simest_grant_component +
                self.grant_meur_masen +
                self.grant_meur_iresen +
                self.grant_meur_connection +
                self.grant_meur_cri)
    
    def calculate_grant_percentage(self) -> float:
        """Calculate grant percentage of CAPEX."""
        if self.capex_meur <= 0:
            return 0.0
        return (self.calculate_total_grants() / self.capex_meur) * 100
    
    def calculate_capacity_factor(self) -> float:
        """Calculate capacity factor."""
        if self.capacity_mw <= 0:
            return 0.0
        return self.production_mwh_year1 / (self.capacity_mw * 8760)
    
    def calculate_specific_capex(self) -> float:
        """Calculate specific CAPEX (EUR/kW)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.capex_meur * 1e6) / (self.capacity_mw * 1000)
    
    def calculate_specific_opex(self) -> float:
        """Calculate specific OPEX (EUR/kW/year)."""
        if self.capacity_mw <= 0:
            return 0.0
        return (self.opex_keuros_year1 * 1000) / (self.capacity_mw * 1000)

    @property
    def equity_percentage(self) -> float:
        """Calculate equity percentage from debt ratio."""
        return 1.0 - self.debt_ratio

    @property
    def equity_meur(self) -> float:
        """Calculate equity amount in MEUR."""
        return self.capex_meur * self.equity_percentage

    @property
    def debt_meur(self) -> float:
        """Calculate debt amount in MEUR."""
        return self.capex_meur * self.debt_ratio

    @property
    def investment_for_debt_sizing_meur(self) -> float:
        """Calculate investment amount for debt sizing, accounting for SIMEST soft loan."""
        # CAPEX minus grants, but SIMEST soft loan is additional debt financing
        base_investment = self.capex_meur - self.calculate_total_grants()
        simest_soft_loan = self.calculate_simest_soft_loan_component()
        return base_investment + simest_soft_loan

    @property
    def simest_total_facility_meur(self) -> float:
        """Total SIMEST facility amount."""
        return self._grant_meur_piano_mattei_unified

    @property
    def simest_grant_meur(self) -> float:
        """SIMEST non-repayable grant component."""
        return self.calculate_simest_grant_component()

    @property
    def simest_soft_loan_meur(self) -> float:
        """SIMEST soft loan component."""
        return self.calculate_simest_soft_loan_component()

    @property
    def opex_meur_per_year(self) -> float:
        """Convert OPEX to MEUR per year."""
        return self.opex_keuros_year1 / 1000.0
    
    def get_financial_summary(self) -> Dict[str, Any]:
        """Get financial summary for display."""
        return {
            'capacity_mw': self.capacity_mw,
            'capex_meur': self.capex_meur,
            'specific_capex_eur_kw': self.calculate_specific_capex(),
            'opex_keuros_year1': self.opex_keuros_year1,
            'specific_opex_eur_kw_year': self.calculate_specific_opex(),
            'production_mwh_year1': self.production_mwh_year1,
            'capacity_factor': self.calculate_capacity_factor(),
            'ppa_price_eur_kwh': self.ppa_price_eur_kwh,
            'total_grants_meur': self.calculate_total_grants(),
            'grant_percentage': self.calculate_grant_percentage(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'discount_rate': self.discount_rate
        }
    
    def get_validation_status(self) -> Dict[str, Any]:
        """Get validation status for UI."""
        return {
            'is_valid': self.is_validated,
            'error_count': len(self.validation_errors),
            'errors': self.validation_errors,
            'warnings': self._get_warnings()
        }
    
    def _get_warnings(self) -> Dict[str, str]:
        """Get warnings for parameters that are valid but potentially concerning."""
        warnings = {}
        
        # Check for high debt ratio
        if self.debt_ratio > 0.8:
            warnings['debt_ratio'] = "High debt ratio may increase financial risk"
        
        # Check for low capacity factor
        capacity_factor = self.calculate_capacity_factor()
        if capacity_factor < 0.2:
            warnings['capacity_factor'] = "Low capacity factor may impact economics"
        
        # Check for high specific CAPEX
        specific_capex = self.calculate_specific_capex()
        if specific_capex > 1500:  # EUR/kW
            warnings['capex'] = "High specific CAPEX compared to industry benchmarks"
        
        # Check for low PPA price
        if self.ppa_price_eur_kwh < 0.03:
            warnings['ppa_price'] = "Low PPA price may impact project viability"
        
        return warnings
    
    def copy_with_modifications(self, **kwargs) -> 'EnhancedProjectAssumptions':
        """Create a copy with modifications for scenario analysis."""
        current_dict = self.to_dict()
        current_dict.update(kwargs)
        return EnhancedProjectAssumptions.from_dict(current_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        # Get all dataclass fields
        import dataclasses
        base_dict = dataclasses.asdict(self)

        # Add legacy fields for backward compatibility
        # Both map to the unified field but are included for compatibility
        base_dict['grant_meur_italy'] = self._grant_meur_piano_mattei_unified
        base_dict['grant_meur_simest_africa'] = 0.0  # Always 0 to prevent double-counting

        # Remove calculated fields that shouldn't be serialized for reconstruction
        calculated_fields = {'total_grant_meur_maroc', 'total_grants_meur', 'investment_for_debt_sizing_meur'}
        for field in calculated_fields:
            base_dict.pop(field, None)

        return base_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnhancedProjectAssumptions':
        """Create instance from dictionary."""
        # Handle case where data is already an EnhancedProjectAssumptions object
        if isinstance(data, cls):
            return data

        # Handle case where data is not a dictionary
        if not isinstance(data, dict):
            raise TypeError(f"Expected dict, got {type(data)}")

        # Create a copy to avoid modifying the original data
        data_copy = data.copy()

        # Handle Piano Mattei/SIMEST migration - consolidate legacy fields
        grant_italy = data_copy.get('grant_meur_italy', 0.0)
        grant_simest = data_copy.get('grant_meur_simest_africa', 0.0)

        # If both legacy fields are populated, sum them and log the migration
        if grant_italy > 0 and grant_simest > 0:
            unified_value = grant_italy + grant_simest
            print(f"Migration: Consolidating Piano Mattei ({grant_italy}) + SIMEST ({grant_simest}) = {unified_value} MEUR")
            data_copy['_grant_meur_piano_mattei_unified'] = unified_value
        elif grant_italy > 0:
            data_copy['_grant_meur_piano_mattei_unified'] = grant_italy
        elif grant_simest > 0:
            data_copy['_grant_meur_piano_mattei_unified'] = grant_simest
        else:
            data_copy['_grant_meur_piano_mattei_unified'] = 0.0

        # Remove legacy fields from data_copy to prevent conflicts
        data_copy.pop('grant_meur_italy', None)
        data_copy.pop('grant_meur_simest_africa', None)

        # Handle legacy/invalid field names and calculated fields
        calculated_fields = {'total_grant_meur_maroc', 'total_grants_meur', 'investment_for_debt_sizing_meur'}

        # Remove calculated fields that shouldn't be passed to constructor
        for field in calculated_fields:
            if field in data_copy:
                del data_copy[field]

        # Filter out fields that don't belong to the dataclass or can't be initialized
        valid_fields = {
            k: v for k, v in data_copy.items()
            if k in cls.__dataclass_fields__ and
            cls.__dataclass_fields__[k].init
        }
        return cls(**valid_fields)
    
    def get_benchmark_comparison(self) -> Dict[str, Dict[str, Any]]:
        """Compare against industry benchmarks."""
        benchmarks = {
            'capacity_factor': {'value': 0.25, 'unit': '', 'description': 'Industry average'},
            'specific_capex': {'value': 1000, 'unit': 'EUR/kW', 'description': 'Industry average'},
            'specific_opex': {'value': 15, 'unit': 'EUR/kW/year', 'description': 'Industry average'},
            'debt_ratio': {'value': 0.75, 'unit': '', 'description': 'Typical project finance'},
            'interest_rate': {'value': 0.06, 'unit': '', 'description': 'Current market rates'},
            'project_irr_target': {'value': 0.12, 'unit': '', 'description': 'Minimum acceptable'}
        }
        
        current_values = {
            'capacity_factor': self.calculate_capacity_factor(),
            'specific_capex': self.calculate_specific_capex(),
            'specific_opex': self.calculate_specific_opex(),
            'debt_ratio': self.debt_ratio,
            'interest_rate': self.interest_rate,
            'project_irr_target': self.discount_rate  # Using discount rate as proxy
        }
        
        comparison = {}
        for metric, benchmark in benchmarks.items():
            current = current_values.get(metric, 0)
            comparison[metric] = {
                'current': current,
                'benchmark': benchmark['value'],
                'unit': benchmark['unit'],
                'description': benchmark['description'],
                'ratio': current / benchmark['value'] if benchmark['value'] != 0 else 0,
                'status': 'good' if abs(current - benchmark['value']) / benchmark['value'] < 0.2 else 'warning'
            }
        
        return comparison

    # SIMEST Split Structure Methods

    def calculate_simest_grant_component(self) -> float:
        """Calculate the non-repayable grant portion of SIMEST facility.
        
        Returns 10% or 20% of total SIMEST facility based on company location.
        
        Returns:
            float: Grant component in MEUR
        """
        if self._grant_meur_piano_mattei_unified <= 0:
            return 0.0
        
        try:
            grant_percentage = self._get_simest_grant_percentage()
            return self._grant_meur_piano_mattei_unified * grant_percentage
        except Exception as e:
            self.logger.warning(f"Error calculating SIMEST grant component: {e}")
            # Fallback to 10% if config unavailable
            return self._grant_meur_piano_mattei_unified * 0.10

    def calculate_simest_soft_loan_component(self) -> float:
        """Calculate the soft loan portion of SIMEST facility.
        
        Returns 80-90% of total SIMEST facility based on company location.
        
        Returns:
            float: Soft loan component in MEUR
        """
        if self._grant_meur_piano_mattei_unified <= 0:
            return 0.0
        
        try:
            grant_percentage = self._get_simest_grant_percentage()
            soft_loan_percentage = 1.0 - grant_percentage
            return self._grant_meur_piano_mattei_unified * soft_loan_percentage
        except Exception as e:
            self.logger.warning(f"Error calculating SIMEST soft loan component: {e}")
            # Fallback to 90% if config unavailable
            return self._grant_meur_piano_mattei_unified * 0.90

    def get_simest_soft_loan_terms(self) -> Dict[str, Any]:
        """Get SIMEST soft loan terms from configuration.
        
        Returns:
            Dict containing interest_rate, tenor_years, grace_years
        """
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_soft_loan_terms()
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST soft loan terms: {e}")
            # Fallback to default terms
            return {
                'interest_rate': 0.00511,  # 0.511%
                'tenor_years': 6,
                'grace_years': 2
            }

    def _get_simest_grant_percentage(self) -> float:
        """Get SIMEST grant percentage based on company location."""
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_grant_percentage(self.company_southern_italy)
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST grant percentage: {e}")
            # Fallback based on company_southern_italy flag
            return 0.20 if self.company_southern_italy else 0.10

    def _get_simest_eligible_countries(self) -> list:
        """Get list of SIMEST eligible countries from configuration."""
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.get_simest_eligible_countries()
        except Exception as e:
            self.logger.warning(f"Error getting SIMEST eligible countries: {e}")
            # Fallback to basic African countries list
            return ['morocco', 'algeria', 'tunisia', 'egypt', 'libya', 'sudan', 'ethiopia', 
                   'kenya', 'uganda', 'tanzania', 'rwanda', 'burundi', 'nigeria', 'ghana', 
                   'south_africa', 'angola', 'mozambique', 'zambia', 'zimbabwe', 'botswana']

    def _validate_simest_eligibility(self) -> Dict[str, Any]:
        """Validate SIMEST program eligibility."""
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            return config_service.validate_simest_eligibility(
                self.project_location, 
                self.company_location
            )
        except Exception as e:
            self.logger.warning(f"Error validating SIMEST eligibility: {e}")
            # Fallback validation
            eligible_countries = self._get_simest_eligible_countries()
            errors = []
            
            if self.project_location.lower() not in [c.lower() for c in eligible_countries]:
                errors.append(f"Project location '{self.project_location}' is not eligible for SIMEST")
            
            if 'italy' not in self.company_location.lower():
                errors.append(f"Company location '{self.company_location}' must be in Italy for SIMEST eligibility")
            
            return {'is_eligible': not errors, 'errors': errors}

    def _validate_simest_facility_amounts(self) -> Dict[str, str]:
        """Validate SIMEST facility amounts against program limits."""
        errors = {}
        
        if self._grant_meur_piano_mattei_unified <= 0:
            return errors
        
        try:
            from services.config_service import ConfigurationService
            config_service = ConfigurationService()
            config = config_service.load_simest_config()
            
            min_facility = config.get('minimum_facility_meur', 0.1)
            max_facility = config.get('maximum_facility_meur', 50.0)
            
            if self._grant_meur_piano_mattei_unified < min_facility:
                errors['simest_min_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) is below minimum ({min_facility} MEUR)"
            
            if self._grant_meur_piano_mattei_unified > max_facility:
                errors['simest_max_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) exceeds maximum ({max_facility} MEUR)"
                
        except Exception as e:
            self.logger.warning(f"Error validating SIMEST facility amounts: {e}")
            # Fallback validation with default limits
            if self._grant_meur_piano_mattei_unified < 0.1:
                errors['simest_min_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) is below minimum (0.1 MEUR)"
            
            if self._grant_meur_piano_mattei_unified > 50.0:
                errors['simest_max_facility'] = f"SIMEST facility amount ({self._grant_meur_piano_mattei_unified:.2f} MEUR) exceeds maximum (50.0 MEUR)"
        
        return errors

    def _validate_simest_grant_limits(self) -> Dict[str, str]:
        """Validate SIMEST grant component against CAPEX limits."""
        errors = {}
        
        if self._grant_meur_piano_mattei_unified <= 0:
            return errors
        
        grant_component = self.calculate_simest_grant_component()
        grant_percentage = self._get_simest_grant_percentage()
        max_grant_percentage = grant_percentage  # 10% or 20%
        
        # Check if grant component exceeds the allowed percentage of CAPEX
        max_allowed_grant = self.capex_meur * max_grant_percentage
        
        if grant_component > max_allowed_grant:
            errors['simest_grant_limit'] = f"SIMEST grant component ({grant_component:.2f} MEUR) exceeds {max_grant_percentage*100:.0f}% of CAPEX limit ({max_allowed_grant:.2f} MEUR)"
        
        return errors

    # Property aliases for backward compatibility and unified grant handling
    @property
    def grant_meur_italy(self) -> float:
        """Legacy property for Italian grants (Piano Mattei).

        Maps to the unified Piano Mattei/SIMEST field to prevent double-counting.
        Piano Mattei budget is managed through SIMEST.
        """
        return self._grant_meur_piano_mattei_unified

    @grant_meur_italy.setter
    def grant_meur_italy(self, value: float):
        """Set Italian grants value in the unified field."""
        self._grant_meur_piano_mattei_unified = value

    @property
    def grant_meur_simest_africa(self) -> float:
        """Legacy property for SIMEST African Fund.

        Maps to the unified Piano Mattei/SIMEST field to prevent double-counting.
        Piano Mattei budget is managed through SIMEST.
        """
        return self._grant_meur_piano_mattei_unified

    @grant_meur_simest_africa.setter
    def grant_meur_simest_africa(self, value: float):
        """Set SIMEST African Fund value in the unified field."""
        self._grant_meur_piano_mattei_unified = value
