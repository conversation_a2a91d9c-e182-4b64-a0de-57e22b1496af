"""
Configuration Service
====================

Service for managing application configurations with JSON support.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
from functools import lru_cache

from config import AppConfig, UIConfig, ExportConfig
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


class ConfigurationService:
    """Service for managing application configurations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config_dir = Path("config")
        self.templates_dir = self.config_dir / "templates"
        self.simest_config_path = self.config_dir / "simest_config.json"
        self._simest_config: Optional[Dict[str, Any]] = None
        
        # Ensure directories exist
        self.config_dir.mkdir(exist_ok=True)
        self.templates_dir.mkdir(exist_ok=True)
        
        # Load and validate SIMEST config at startup
        try:
            self.load_simest_config()
            self.validate_simest_config(self._simest_config)
        except Exception as e:
            self.logger.error(f"SIMEST configuration error during initialization: {e}")
    
    def load_configuration_from_json(self, config_file: Path) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            self.logger.info(f"Configuration loaded from {config_file}")
            return config_data
        except FileNotFoundError:
            self.logger.error(f"Configuration file not found: {config_file}")
            raise
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Error loading configuration: {str(e)}")
            raise
    
    def save_configuration_to_json(self, 
                                   app_config: AppConfig,
                                   ui_config: UIConfig,
                                   export_config: ExportConfig,
                                   config_file: Path,
                                   include_metadata: bool = True) -> bool:
        """Save configuration to JSON file."""
        try:
            config_data = {
                'app': app_config.to_dict(),
                'ui': {
                    'theme_mode': ui_config.theme_mode,
                    'enable_animations': ui_config.enable_animations,
                    'animation_duration': ui_config.animation_duration
                },
                'export': {
                    'default_formats': export_config.default_formats,
                    'chart_dpi': export_config.chart_dpi,
                    'chart_color_scheme': export_config.chart_color_scheme,
                    'chart_types_enabled': export_config.chart_types_enabled
                }
            }
            # Include SIMEST configuration in saved config
            try:
                config_data['simest'] = self.load_simest_config()
            except Exception as e:
                self.logger.warning(f"Could not include SIMEST config in save: {e}")
            
            if include_metadata:
                config_data['metadata'] = {
                    'saved_at': datetime.now().isoformat(),
                    'version': app_config.app_version,
                    'created_by': app_config.company_name
                }
            
            # Ensure parent directory exists
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to {config_file}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving configuration: {str(e)}")
            return False
    
    def apply_configuration(self,
                            config_data: Dict[str, Any],
                            app_config: AppConfig,
                            ui_config: UIConfig,
                            export_config: ExportConfig) -> bool:
        """Apply loaded configuration to config objects."""
        try:
            # Apply app configuration
            if 'app' in config_data:
                app_data = config_data['app']
                for key, value in app_data.items():
                    if hasattr(app_config, key):
                        setattr(app_config, key, value)
            
            # Apply UI configuration
            if 'ui' in config_data:
                ui_data = config_data['ui']
                for key, value in ui_data.items():
                    if hasattr(ui_config, key):
                        setattr(ui_config, key, value)
            
            # Apply export configuration
            if 'export' in config_data:
                export_data = config_data['export']
                for key, value in export_data.items():
                    if hasattr(export_config, key):
                        setattr(export_config, key, value)
            
            # If imported config contains SIMEST settings, refresh local file
            if 'simest' in config_data:
                try:
                    simest_data = config_data['simest']
                    with open(self.simest_config_path, 'w', encoding='utf-8') as f:
                        json.dump(simest_data, f, indent=2, ensure_ascii=False)
                    self.refresh_simest_config()
                    self.logger.info("SIMEST configuration updated from import")
                except Exception as e:
                    self.logger.warning(f"Failed to apply SIMEST configuration from import: {e}")
            
            self.logger.info("Configuration applied successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error applying configuration: {str(e)}")
            return False
    
    def create_project_template(self,
                                template_name: str,
                                client_profile: ClientProfile,
                                project_assumptions: EnhancedProjectAssumptions,
                                description: str = "") -> bool:
        """Create a project template from current settings."""
        try:
            template_data = {
                'name': template_name,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'client_profile': client_profile.to_dict(),
                'project_assumptions': project_assumptions.to_dict()
            }
            
            template_file = self.templates_dir / f"{template_name.lower().replace(' ', '_')}.json"
            
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Project template '{template_name}' saved")
            return True
        except Exception as e:
            self.logger.error(f"Error creating project template: {str(e)}")
            return False
    
    def list_available_templates(self) -> List[Dict[str, Any]]:
        """List all available project templates."""
        templates = []
        try:
            for template_file in self.templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                    templates.append({
                        'file': template_file,
                        'name': template_data.get('name', template_file.stem),
                        'description': template_data.get('description', ''),
                        'created_at': template_data.get('created_at', '')
                    })
                except Exception:
                    continue
        except Exception as e:
            self.logger.error(f"Error listing templates: {str(e)}")
        return templates
    
    def delete_template(self, template_file: Path) -> bool:
        """Delete a project template."""
        try:
            template_file.unlink()
            self.logger.info(f"Template deleted: {template_file}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting template: {str(e)}")
            return False
    
    def export_full_configuration(self,
                                  app_config: AppConfig,
                                  ui_config: UIConfig,
                                  export_config: ExportConfig,
                                  client_profile: Optional[ClientProfile] = None,
                                  project_assumptions: Optional[EnhancedProjectAssumptions] = None,
                                  output_file: Optional[Path] = None) -> Path:
        """Export complete application and project configuration."""
        try:
            if output_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = self.config_dir / f"full_config_export_{timestamp}.json"
            
            config_data = {
                'export_info': {
                    'exported_at': datetime.now().isoformat(),
                    'exported_by': app_config.company_name,
                    'app_version': app_config.app_version,
                    'export_type': 'full_configuration'
                },
                'app_config': app_config.to_dict(),
                'ui_config': {
                    'theme_mode': ui_config.theme_mode,
                    'enable_animations': ui_config.enable_animations,
                    'animation_duration': ui_config.animation_duration,
                    'chart_height': ui_config.chart_height,
                    'chart_width': ui_config.chart_width
                },
                'export_config': {
                    'default_formats': export_config.default_formats,
                    'chart_settings': export_config.get_chart_settings(),
                    'excel_settings': export_config.get_excel_settings(),
                    'html_settings': export_config.get_html_settings()
                },
                'simest_config': self.load_simest_config()
            }
            
            if client_profile:
                config_data['client_profile'] = client_profile.to_dict()
            
            if project_assumptions:
                config_data['project_assumptions'] = project_assumptions.to_dict()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.info(f"Full configuration exported to {output_file}")
            return output_file
        except Exception as e:
            self.logger.error(f"Error exporting full configuration: {str(e)}")
            raise
    
    def import_full_configuration(self, config_file: Path) -> Dict[str, Any]:
        """Import complete application and project configuration."""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Validate the configuration format
            if 'export_info' not in config_data:
                raise ValueError("Invalid configuration file format")
            
            export_info = config_data['export_info']
            self.logger.info(f"Importing configuration from {export_info.get('exported_at', 'unknown date')}")
            return config_data
        except Exception as e:
            self.logger.error(f"Error importing full configuration: {str(e)}")
            raise
    
    def validate_configuration(self, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate configuration data and return validation results."""
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'info': []
        }
        try:
            # Check for required sections
            required_sections = ['app']
            for section in required_sections:
                if section not in config_data:
                    validation_results['errors'].append(f"Missing required section: {section}")
                    validation_results['is_valid'] = False
            
            # Validate app configuration
            if 'app' in config_data:
                app_data = config_data['app']
                required_app_fields = ['app_name', 'company_name']
                for field in required_app_fields:
                    if not app_data.get(field):
                        validation_results['warnings'].append(f"Missing or empty app field: {field}")
                if 'auto_save' in app_data and not isinstance(app_data['auto_save'], bool):
                    validation_results['errors'].append("app.auto_save must be a boolean")
                    validation_results['is_valid'] = False
            
            # Validate export configuration
            if 'export' in config_data:
                export_data = config_data['export']
                if 'chart_dpi' in export_data:
                    dpi = export_data['chart_dpi']
                    if not isinstance(dpi, int) or dpi < 50 or dpi > 1200:
                        validation_results['warnings'].append("chart_dpi should be between 50 and 1200")
                if 'default_formats' in export_data:
                    formats = export_data['default_formats']
                    if not isinstance(formats, list):
                        validation_results['errors'].append("default_formats must be a list")
                        validation_results['is_valid'] = False
            
            validation_results['info'].append(f"Configuration contains {len(config_data)} main sections")
            if validation_results['is_valid']:
                validation_results['info'].append("Configuration validation passed")
        except Exception as e:
            validation_results['errors'].append(f"Validation error: {str(e)}")
            validation_results['is_valid'] = False
        
        return validation_results
    
    def get_default_config_paths(self) -> Dict[str, Path]:
        """Get default configuration file paths."""
        return {
            'app_config': self.config_dir / "app_config.json",
            'saved_config': self.config_dir / "saved_config.json",
            'example_config': self.config_dir / "example_config.json",
            'templates_dir': self.templates_dir
        }
    
    def create_default_configuration_files(self) -> bool:
        """Create default configuration files if they don't exist."""
        try:
            paths = self.get_default_config_paths()
            
            # Create example configuration if it doesn't exist
            if not paths['example_config'].exists():
                default_app = AppConfig()
                default_ui = UIConfig()
                default_export = ExportConfig()
                
                self.save_configuration_to_json(
                    default_app, default_ui, default_export,
                    paths['example_config']
                )
            
            self.logger.info("Default configuration files created")
            return True
        except Exception as e:
            self.logger.error(f"Error creating default configuration files: {str(e)}")
            return False

    # SIMEST Configuration Methods

    def load_simest_config(self) -> Dict[str, Any]:
        """Load SIMEST configuration from JSON, with caching."""
        if self._simest_config is None:
            try:
                self._simest_config = self.load_configuration_from_json(self.simest_config_path)
                self.logger.info("SIMEST configuration loaded")
            except Exception as e:
                self.logger.error(f"Failed to load SIMEST configuration: {e}")
                raise
        return self._simest_config

    def refresh_simest_config(self) -> Dict[str, Any]:
        """Refresh (reload) SIMEST configuration."""
        self._simest_config = None
        return self.load_simest_config()

    def validate_simest_config(self, config: Dict[str, Any]) -> bool:
        """Validate the SIMEST configuration parameters."""
        errors = []
        required_keys = [
            'program_name', 'fund_size_meur', 'grant_percentage_standard',
            'grant_percentage_southern_italy', 'soft_loan_interest_rate',
            'soft_loan_tenor_years', 'soft_loan_grace_years',
            'eligible_countries', 'minimum_facility_meur', 'maximum_facility_meur',
            'eligible_activities', 'company_eligibility'
        ]
        for key in required_keys:
            if key not in config:
                errors.append(f"Missing SIMEST config key: {key}")
        # Validate values
        if 'grant_percentage_standard' in config:
            v = config['grant_percentage_standard']
            if not isinstance(v, (int, float)) or not 0 <= v <= 1:
                errors.append("grant_percentage_standard must be between 0 and 1")
        if 'grant_percentage_southern_italy' in config:
            v = config['grant_percentage_southern_italy']
            if not isinstance(v, (int, float)) or not 0 <= v <= 1:
                errors.append("grant_percentage_southern_italy must be between 0 and 1")
        if 'soft_loan_interest_rate' in config:
            v = config['soft_loan_interest_rate']
            if not isinstance(v, (int, float)) or v < 0:
                errors.append("soft_loan_interest_rate must be non-negative")
        if 'soft_loan_tenor_years' in config:
            v = config['soft_loan_tenor_years']
            if not isinstance(v, int) or v <= 0:
                errors.append("soft_loan_tenor_years must be a positive integer")
        if 'soft_loan_grace_years' in config:
            v = config['soft_loan_grace_years']
            if not isinstance(v, int) or v < 0:
                errors.append("soft_loan_grace_years must be a non-negative integer")
        if 'eligible_countries' in config:
            v = config['eligible_countries']
            if not isinstance(v, list) or not v:
                errors.append("eligible_countries must be a non-empty list")
        if 'minimum_facility_meur' in config and 'maximum_facility_meur' in config:
            min_f = config['minimum_facility_meur']
            max_f = config['maximum_facility_meur']
            if not isinstance(min_f, (int, float)) or min_f < 0:
                errors.append("minimum_facility_meur must be non-negative")
            if not isinstance(max_f, (int, float)) or max_f < min_f:
                errors.append("maximum_facility_meur must be >= minimum_facility_meur")
        if errors:
            msg = "; ".join(errors)
            self.logger.error(f"SIMEST config validation errors: {msg}")
            raise ValueError(f"SIMEST config validation errors: {msg}")
        self.logger.info("SIMEST configuration validated")
        return True

    def get_simest_grant_percentage(self, is_southern_italy: bool) -> float:
        """Return the SIMEST grant percentage based on company location."""
        config = self.load_simest_config()
        if is_southern_italy:
            return config['grant_percentage_southern_italy']
        return config['grant_percentage_standard']

    def get_simest_soft_loan_terms(self) -> Dict[str, Any]:
        """Return the SIMEST soft loan terms: interest rate, tenor, grace."""
        config = self.load_simest_config()
        return {
            'interest_rate': config['soft_loan_interest_rate'],
            'tenor_years': config['soft_loan_tenor_years'],
            'grace_years': config['soft_loan_grace_years']
        }

    def get_simest_eligible_countries(self) -> List[str]:
        """Return the list of eligible countries for SIMEST program."""
        config = self.load_simest_config()
        return config['eligible_countries']

    def validate_simest_eligibility(self, project_location: str, company_location: str) -> Dict[str, Any]:
        """
        Validate SIMEST eligibility for a given project and company location.
        Returns dict with 'is_eligible' bool and 'errors' list.
        """
        errors: List[str] = []
        config = self.load_simest_config()
        # Project location check
        if project_location.lower() not in [c.lower() for c in config['eligible_countries']]:
            errors.append(f"Project location '{project_location}' is not eligible for SIMEST")
        # Company location check: must be in Italy
        if 'italy' not in company_location.lower():
            errors.append(f"Company location '{company_location}' must be in Italy for SIMEST eligibility")
        return {'is_eligible': not errors, 'errors': errors}