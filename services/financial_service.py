"""
Financial Model Service
=======================

Service for financial modeling calculations and analysis.
"""

from typing import Dict, Any, Optional, Callable, List
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Use local enhanced DCF model for 2025 standards
from .enhanced_dcf_model import EnhancedDCFModel, EnhancedDCFAssumptions
from .error_handler import error_handler, ErrorSeverity, FallbackManager, FinancialModelError
from .cache_service import cached_result
from models.project_assumptions import EnhancedProjectAssumptions


class FinancialModelService:
    """Enhanced Financial Model Service with 2025 DCF standards."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.dcf_model = EnhancedDCFModel()
        self._current_results: Optional[Dict[str, Any]] = None
        self._sensitivity_results: Optional[pd.DataFrame] = None
        self._monte_carlo_results: Optional[Dict[str, Any]] = None
        self._scenario_results: Optional[Dict[str, Any]] = None
    
    @error_handler(severity=ErrorSeverity.HIGH,
                  user_message="Financial model calculation failed. Please check your inputs and try again.",
                  fallback_return=None)
    @cached_result(ttl=1800, key_prefix="financial_model")  # Cache for 30 minutes
    def run_financial_model(self,
                          assumptions: EnhancedProjectAssumptions,
                          progress_callback: Optional[Callable[[float, str], None]] = None,
                          detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None) -> Dict[str, Any]:
        """Run the enhanced DCF financial model with 2025 standards and SIMEST split structure."""
        try:
            if progress_callback:
                progress_callback(10, "Validating assumptions...")
            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "validate_inputs", 0, "Starting validation")

            # Validate assumptions including SIMEST compliance
            validation_errors = assumptions.validate_all()
            if validation_errors:
                raise ValueError(f"Validation errors: {validation_errors}")
            
            # Additional SIMEST program compliance validation
            simest_validation = self._validate_simest_compliance(assumptions)
            if simest_validation['errors']:
                self.logger.warning(f"SIMEST compliance warnings: {simest_validation['errors']}")

            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "validate_inputs", 100, "Validation completed")

            if progress_callback:
                progress_callback(30, "Building enhanced DCF cashflow model...")
            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "dcf_calculation", 0, "Building DCF model")

            # Convert to DCF assumptions format
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "dcf_calculation", 30, "DCF assumptions prepared")

            # Build enhanced cashflow with DCF model
            cashflow = self.dcf_model.build_cashflow(dcf_assumptions)

            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "dcf_calculation", 100, "DCF cashflow model built")

            if progress_callback:
                progress_callback(60, "Computing enhanced KPIs and DCF metrics...")
            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "kpi_calculation", 0, "Starting KPI calculation")

            # Compute comprehensive KPIs
            kpis = self.dcf_model.compute_kpis(cashflow, dcf_assumptions)

            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "kpi_calculation", 100, "KPI calculation completed")

            if progress_callback:
                progress_callback(80, "Running ML predictions...")
            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "ml_prediction", 0, "Starting ML predictions")

            # Run ML predictions if available
            try:
                from services.ml_prediction_service import predict_financial_metrics
                ml_predictions = predict_financial_metrics(assumptions.to_dict())
                if detailed_progress_callback:
                    detailed_progress_callback("financial_calculation", "ml_prediction", 100, "ML predictions completed")
            except Exception as e:
                self.logger.warning(f"ML predictions failed: {e}")
                ml_predictions = {}
                if detailed_progress_callback:
                    detailed_progress_callback("financial_calculation", "ml_prediction", 100, "ML predictions skipped")

            if progress_callback:
                progress_callback(90, "Finalizing results with DCF analysis...")
            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "finalize_results", 0, "Finalizing results")

            # Store enhanced results
            self._current_results = {
                'cashflow': cashflow,
                'kpis': kpis,
                'assumptions': assumptions.to_dict(),
                'dcf_assumptions': dcf_assumptions.__dict__,
                'ml_predictions': ml_predictions,
                'execution_time': datetime.now().isoformat(),
                'model_version': '3.0.0_DCF_Enhanced'
            }

            if detailed_progress_callback:
                detailed_progress_callback("financial_calculation", "finalize_results", 100, "Results finalized")

            if progress_callback:
                progress_callback(100, "Enhanced DCF model completed successfully")

            self.logger.info("Enhanced DCF financial model executed successfully")
            return self._current_results

        except Exception as e:
            self.logger.error(f"Error running enhanced DCF financial model: {str(e)}")
            # Return fallback results to maintain application stability
            fallback_results = FallbackManager.get_fallback_financial_results()
            fallback_results['error_message'] = str(e)
            return fallback_results
    
    @error_handler(severity=ErrorSeverity.MEDIUM,
                  user_message="Sensitivity analysis failed. Using default parameters.",
                  fallback_return=None)
    def run_sensitivity_analysis(self,
                               assumptions: EnhancedProjectAssumptions,
                               variables: Optional[list] = None,
                               progress_callback: Optional[Callable[[float, str], None]] = None) -> pd.DataFrame:
        """Run sensitivity analysis with SIMEST-aware parameters."""
        try:
            if variables is None:
                # Include SIMEST parameters if they are being used
                variables = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate']
                
                # Add SIMEST parameters if SIMEST is being used
                if assumptions.simest_total_facility_meur > 0:
                    variables.extend(['simest_grant_meur', 'simest_soft_loan_meur', 'total_grants_meur'])
            
            if progress_callback:
                progress_callback(20, "Setting up sensitivity analysis...")
            
            # Convert to DCF assumptions for enhanced sensitivity analysis
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if progress_callback:
                progress_callback(50, "Running enhanced sensitivity calculations...")

            # Use enhanced sensitivity analysis with DCF model
            self._sensitivity_results = self._run_enhanced_sensitivity_analysis(dcf_assumptions, variables)
            
            if progress_callback:
                progress_callback(100, "Sensitivity analysis completed")
            
            self.logger.info("Sensitivity analysis completed successfully")
            return self._sensitivity_results
            
        except Exception as e:
            self.logger.error(f"Error running sensitivity analysis: {str(e)}")
            # Return fallback sensitivity results
            return FallbackManager.get_fallback_sensitivity_results()
    
    def run_monte_carlo_simulation(self,
                                 assumptions: EnhancedProjectAssumptions,
                                 n_simulations: int = 1000,
                                 progress_callback: Optional[Callable[[float, str], None]] = None,
                                 detailed_progress_callback: Optional[Callable[[str, str, float, str], None]] = None) -> Dict[str, Any]:
        """Run Monte Carlo simulation."""
        try:
            if progress_callback:
                progress_callback(10, "Setting up Monte Carlo simulation...")
            if detailed_progress_callback:
                detailed_progress_callback("monte_carlo_simulation", "setup_simulation", 0, "Initializing Monte Carlo setup")
            
            # Convert to DCF assumptions for enhanced Monte Carlo
            dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if detailed_progress_callback:
                detailed_progress_callback("monte_carlo_simulation", "setup_simulation", 100, "Monte Carlo setup completed")

            if progress_callback:
                progress_callback(30, f"Running {n_simulations} enhanced simulations...")
            if detailed_progress_callback:
                detailed_progress_callback("monte_carlo_simulation", "run_simulations", 0, f"Starting {n_simulations} simulations")

            # Run enhanced Monte Carlo simulation with DCF model
            mc_results = self._run_enhanced_monte_carlo(dcf_assumptions, n_simulations, progress_callback, detailed_progress_callback)
            
            if progress_callback:
                progress_callback(80, "Generating statistics...")
            if detailed_progress_callback:
                detailed_progress_callback("monte_carlo_simulation", "analyze_results", 0, "Analyzing simulation results")

            # Statistics are already generated in _run_enhanced_monte_carlo
            self._monte_carlo_results = {
                'results': mc_results['results'],
                'statistics': mc_results['statistics'],
                'risk_metrics': mc_results.get('risk_metrics', {}),
                'correlation_matrix': mc_results.get('correlation_matrix', {}),
                'input_correlation_matrix': mc_results.get('input_correlation_matrix', []),
                'input_parameters': mc_results.get('input_parameters', []),
                'n_simulations': n_simulations,
                'success_rate': mc_results.get('success_rate', 1.0)
            }
            
            if detailed_progress_callback:
                detailed_progress_callback("monte_carlo_simulation", "analyze_results", 100, "Results analysis completed")
            
            if progress_callback:
                progress_callback(100, "Monte Carlo simulation completed")
            
            self.logger.info(f"Monte Carlo simulation completed with {n_simulations} simulations")
            return self._monte_carlo_results
            
        except Exception as e:
            self.logger.error(f"Error running Monte Carlo simulation: {str(e)}")
            raise
    
    def run_scenario_analysis(self,
                            assumptions: EnhancedProjectAssumptions,
                            scenarios: Optional[List[str]] = None,
                            progress_callback: Optional[Callable[[float, str], None]] = None) -> Dict[str, Any]:
        """Run scenario analysis."""
        try:
            if scenarios is None:
                scenarios = ["Base", "Optimistic", "Pessimistic"]

            if progress_callback:
                progress_callback(20, "Setting up scenarios...")

            # Convert to DCF assumptions for enhanced scenario analysis
            base_dcf_assumptions = self._convert_to_dcf_assumptions(assumptions)

            if progress_callback:
                progress_callback(50, "Running scenario calculations...")

            # Run enhanced scenario analysis with DCF model
            self._scenario_results = self._run_enhanced_scenario_analysis(base_dcf_assumptions, scenarios, progress_callback)

            if progress_callback:
                progress_callback(100, "Scenario analysis completed")

            self.logger.info("Scenario analysis completed successfully")
            return self._scenario_results

        except Exception as e:
            self.logger.error(f"Error running scenario analysis: {str(e)}")
            raise
    
    def _convert_to_dcf_assumptions(self, assumptions: EnhancedProjectAssumptions) -> EnhancedDCFAssumptions:
        """Convert UI assumptions to enhanced DCF assumptions format with SIMEST split structure."""
        # Get SIMEST split components
        simest_grant_component = assumptions.calculate_simest_grant_component()
        simest_soft_loan_component = assumptions.calculate_simest_soft_loan_component()
        simest_terms = assumptions.get_simest_soft_loan_terms()
        
        return EnhancedDCFAssumptions(
            capacity_mw=assumptions.capacity_mw,
            production_mwh_year1=assumptions.production_mwh_year1,
            project_life_years=assumptions.project_life_years,
            capex_meur=assumptions.capex_meur,
            opex_keuros_year1=assumptions.opex_keuros_year1,
            ppa_price_eur_kwh=assumptions.ppa_price_eur_kwh,
            ppa_escalation=assumptions.ppa_escalation,
            debt_ratio=assumptions.debt_ratio,
            interest_rate=assumptions.interest_rate,
            debt_years=assumptions.debt_years,
            grace_years=getattr(assumptions, 'grace_years', 2),
            discount_rate=assumptions.discount_rate,
            tax_rate=assumptions.tax_rate,
            tax_holiday=getattr(assumptions, 'tax_holiday', 5),
            degradation_year1=getattr(assumptions, 'degradation_year1', 0.025),
            degradation_annual=getattr(assumptions, 'degradation_rate', 0.005),
            land_lease_eur_mw_year=assumptions.land_lease_eur_mw_year,
            # SIMEST split structure - only grant component in total grants
            total_grants_meur=assumptions.calculate_total_grants(),
            simest_grant_meur=simest_grant_component,
            simest_soft_loan_meur=simest_soft_loan_component,
            simest_interest_rate=simest_terms.get('interest_rate', 0.00511),
            simest_tenor_years=simest_terms.get('tenor_years', 6),
            simest_grace_years=simest_terms.get('grace_years', 2),
            # Enhanced 2025 parameters with defaults
            terminal_growth_rate=0.025,
            terminal_value_method="perpetuity",
            use_terminal_value=True,
            working_capital_days=30,
            insurance_rate=0.002,
            opex_escalation=0.02
        )

    def _run_enhanced_sensitivity_analysis(self, dcf_assumptions, variables):
        """Run enhanced sensitivity analysis with DCF model."""
        try:
            import numpy as np
            import pandas as pd

            # Define sensitivity ranges (±20% for each variable) including SIMEST parameters
            sensitivity_ranges = {
                'production_mwh_year1': [-0.2, -0.1, 0, 0.1, 0.2],
                'ppa_price_eur_kwh': [-0.2, -0.1, 0, 0.1, 0.2],
                'capex_meur': [-0.2, -0.1, 0, 0.1, 0.2],
                'discount_rate': [-0.02, -0.01, 0, 0.01, 0.02],  # Absolute changes for rates
                'opex_keuros_year1': [-0.2, -0.1, 0, 0.1, 0.2],
                # SIMEST-specific parameters
                'simest_grant_meur': [-0.3, -0.15, 0, 0.15, 0.3],  # ±30% for grant component
                'simest_soft_loan_meur': [-0.3, -0.15, 0, 0.15, 0.3],  # ±30% for soft loan component
                'simest_interest_rate': [-0.002, -0.001, 0, 0.001, 0.002],  # ±0.2% absolute for preferential rate
                'total_grants_meur': [-0.3, -0.15, 0, 0.15, 0.3]  # ±30% for total grants
            }

            results = []
            base_assumptions = dcf_assumptions

            # Calculate base case
            base_cashflow = self.dcf_model.build_cashflow(base_assumptions)
            base_kpis = self.dcf_model.compute_kpis(base_cashflow, base_assumptions)
            base_irr = base_kpis.get('IRR_equity', 0)

            for variable in variables:
                if variable not in sensitivity_ranges:
                    continue

                for change in sensitivity_ranges[variable]:
                    # Create modified assumptions
                    modified_assumptions = dcf_assumptions.__class__(**dcf_assumptions.__dict__)

                    current_value = getattr(modified_assumptions, variable)
                    if variable == 'discount_rate':
                        # Absolute change for discount rate
                        new_value = current_value + change
                    else:
                        # Percentage change for other variables
                        new_value = current_value * (1 + change)

                    setattr(modified_assumptions, variable, new_value)

                    # Run model with modified assumptions
                    try:
                        modified_cashflow = self.dcf_model.build_cashflow(modified_assumptions)
                        modified_kpis = self.dcf_model.compute_kpis(modified_cashflow, modified_assumptions)
                        modified_irr = modified_kpis.get('IRR_equity', 0)

                        results.append({
                            'Variable': variable,
                            'Change_%': change * 100,
                            'IRR_equity': modified_irr,
                            'IRR_change_%': (modified_irr - base_irr) * 100,
                            'NPV_equity': modified_kpis.get('NPV_equity', 0),
                            'LCOE': modified_kpis.get('LCOE_eur_kwh', 0)
                        })
                    except Exception as e:
                        self.logger.warning(f"Sensitivity calculation failed for {variable} at {change}: {e}")
                        continue

            return pd.DataFrame(results)

        except Exception as e:
            self.logger.error(f"Enhanced sensitivity analysis failed: {e}")
            # Return empty DataFrame as fallback
            return pd.DataFrame()

    def _run_enhanced_monte_carlo(self, dcf_assumptions, n_simulations, progress_callback=None, detailed_progress_callback=None):
        """Run enhanced Monte Carlo simulation with correlation matrices and industry-specific risk factors."""
        try:
            import numpy as np
            from scipy.stats import multivariate_normal, truncnorm

            results = {
                'IRR_equity': [],
                'IRR_project': [],
                'NPV_equity': [],
                'NPV_project': [],
                'LCOE_eur_kwh': [],
                'Min_DSCR': [],
                'Payback_years': []
            }

            # Enhanced uncertainty parameters with industry-specific distributions including SIMEST
            uncertainty_params = {
                'production_mwh_year1': {
                    'type': 'truncated_normal',
                    'std': 0.12,  # 12% std dev for renewable energy
                    'lower_bound': 0.7,  # Minimum 70% of expected
                    'upper_bound': 1.3   # Maximum 130% of expected
                },
                'ppa_price_eur_kwh': {
                    'type': 'truncated_normal',
                    'std': 0.08,  # 8% std dev for PPA prices
                    'lower_bound': 0.8,
                    'upper_bound': 1.2
                },
                'capex_meur': {
                    'type': 'truncated_normal',
                    'std': 0.18,  # 18% std dev for CAPEX (construction risk)
                    'lower_bound': 0.85,
                    'upper_bound': 1.4
                },
                'opex_keuros_year1': {
                    'type': 'truncated_normal',
                    'std': 0.15,  # 15% std dev for OPEX
                    'lower_bound': 0.8,
                    'upper_bound': 1.3
                },
                'discount_rate': {
                    'type': 'truncated_normal',
                    'std': 0.015,  # 1.5% absolute std dev
                    'lower_bound': 0.05,  # Minimum 5%
                    'upper_bound': 0.15   # Maximum 15%
                },
                'degradation_annual': {
                    'type': 'truncated_normal',
                    'std': 0.002,  # 0.2% absolute std dev
                    'lower_bound': 0.003,  # Minimum 0.3%/year
                    'upper_bound': 0.008   # Maximum 0.8%/year
                },
                'interest_rate': {
                    'type': 'truncated_normal',
                    'std': 0.01,   # 1% absolute std dev
                    'lower_bound': 0.03,   # Minimum 3%
                    'upper_bound': 0.12    # Maximum 12%
                },
                # SIMEST-specific uncertainty parameters
                'simest_grant_meur': {
                    'type': 'truncated_normal',
                    'std': 0.10,  # 10% std dev for grant component (policy risk)
                    'lower_bound': 0.8,
                    'upper_bound': 1.2
                },
                'simest_soft_loan_meur': {
                    'type': 'truncated_normal',
                    'std': 0.15,  # 15% std dev for soft loan availability
                    'lower_bound': 0.7,
                    'upper_bound': 1.3
                },
                'simest_interest_rate': {
                    'type': 'truncated_normal',
                    'std': 0.001,  # 0.1% absolute std dev for preferential rate changes
                    'lower_bound': 0.003,  # Minimum 0.3%
                    'upper_bound': 0.015   # Maximum 1.5%
                }
            }

            # Correlation matrix for renewable energy projects including SIMEST parameters
            param_names = list(uncertainty_params.keys())
            # Extended correlation matrix to include SIMEST parameters
            n_params = len(param_names)
            correlation_matrix = np.eye(n_params)  # Start with identity matrix
            
            # Define correlations based on parameter positions
            param_indices = {name: i for i, name in enumerate(param_names)}
            
            # Base correlations (existing parameters)
            base_correlations = [
                ('production_mwh_year1', 'ppa_price_eur_kwh', 0.15),
                ('production_mwh_year1', 'capex_meur', -0.20),
                ('production_mwh_year1', 'opex_keuros_year1', 0.10),
                ('ppa_price_eur_kwh', 'capex_meur', -0.10),
                ('ppa_price_eur_kwh', 'discount_rate', 0.20),
                ('ppa_price_eur_kwh', 'interest_rate', 0.10),
                ('capex_meur', 'opex_keuros_year1', 0.30),
                ('capex_meur', 'discount_rate', 0.15),
                ('capex_meur', 'interest_rate', 0.25),
                ('opex_keuros_year1', 'discount_rate', 0.10),
                ('opex_keuros_year1', 'degradation_annual', 0.05),
                ('opex_keuros_year1', 'interest_rate', 0.15),
                ('discount_rate', 'interest_rate', 0.60),
                # SIMEST-specific correlations
                ('simest_grant_meur', 'simest_soft_loan_meur', 0.85),  # High correlation within SIMEST program
                ('simest_grant_meur', 'capex_meur', -0.10),  # Slight negative correlation (grants reduce financing need)
                ('simest_soft_loan_meur', 'interest_rate', -0.30),  # Negative correlation (preferential vs market rates)
                ('simest_interest_rate', 'interest_rate', 0.40),  # Moderate correlation with market rates
                ('simest_interest_rate', 'discount_rate', 0.25),  # Some correlation with discount rate
            ]
            
            # Apply correlations to matrix
            for param1, param2, corr in base_correlations:
                if param1 in param_indices and param2 in param_indices:
                    i, j = param_indices[param1], param_indices[param2]
                    correlation_matrix[i, j] = corr
                    correlation_matrix[j, i] = corr  # Symmetric matrix

            # Generate correlated random samples
            mean_vector = np.zeros(len(param_names))

            # Pre-generate all correlated samples for efficiency
            if n_simulations > 100:
                # Use multivariate normal for large simulations
                correlated_samples = multivariate_normal.rvs(
                    mean=mean_vector,
                    cov=correlation_matrix,
                    size=n_simulations
                )
            else:
                # Generate samples one by one for small simulations
                correlated_samples = []
                for _ in range(n_simulations):
                    sample = multivariate_normal.rvs(mean=mean_vector, cov=correlation_matrix)
                    correlated_samples.append(sample)
                correlated_samples = np.array(correlated_samples)

            for i in range(n_simulations):
                # Update progress indicators
                if progress_callback and i % max(1, n_simulations // 10) == 0:
                    progress = 30 + (i / n_simulations) * 60  # 30% to 90%
                    progress_callback(progress, f"Simulation {i+1}/{n_simulations}")
                
                if detailed_progress_callback and i % max(1, n_simulations // 20) == 0:
                    sim_progress = (i / n_simulations) * 100
                    detailed_progress_callback("monte_carlo_simulation", "run_simulations", sim_progress, 
                                             f"Running simulation {i+1}/{n_simulations}")

                # Create random assumptions with correlation
                random_assumptions = dcf_assumptions.__class__(**dcf_assumptions.__dict__)

                # Apply correlated random variations
                for j, param in enumerate(param_names):
                    if hasattr(random_assumptions, param):
                        base_value = getattr(random_assumptions, param)
                        config = uncertainty_params[param]

                        # Get correlated random value
                        z_score = correlated_samples[i][j]

                        if config['type'] == 'truncated_normal':
                            if param in ['discount_rate', 'degradation_annual', 'interest_rate']:
                                # Absolute variation for rates
                                std_dev = config['std']
                                random_value = base_value + z_score * std_dev
                            else:
                                # Relative variation for other parameters
                                std_dev = base_value * config['std']
                                random_value = base_value + z_score * std_dev

                            # Apply bounds
                            if 'lower_bound' in config and 'upper_bound' in config:
                                if param in ['discount_rate', 'degradation_annual', 'interest_rate']:
                                    # Absolute bounds
                                    lower = config['lower_bound']
                                    upper = config['upper_bound']
                                else:
                                    # Relative bounds
                                    lower = base_value * config['lower_bound']
                                    upper = base_value * config['upper_bound']

                                random_value = np.clip(random_value, lower, upper)

                            # Ensure positive values
                            random_value = max(0.001, random_value)
                            setattr(random_assumptions, param, random_value)

                try:
                    # Run DCF model with random assumptions
                    cashflow = self.dcf_model.build_cashflow(random_assumptions)
                    kpis = self.dcf_model.compute_kpis(cashflow, random_assumptions)

                    # Store results with additional risk metrics
                    for key in results.keys():
                        if key in kpis:
                            results[key].append(kpis[key])
                        else:
                            results[key].append(0)

                except Exception as e:
                    self.logger.warning(f"Monte Carlo iteration {i} failed: {e}")
                    # Add NaN values for failed iterations
                    for key in results.keys():
                        results[key].append(np.nan)

            # Generate enhanced statistics with risk metrics
            statistics = {}
            risk_metrics = {}

            for key, values in results.items():
                clean_values = np.array([v for v in values if not np.isnan(v) and np.isfinite(v)])
                if len(clean_values) > 0:
                    statistics[key] = {
                        'mean': float(np.mean(clean_values)),
                        'std': float(np.std(clean_values)),
                        'min': float(np.min(clean_values)),
                        'max': float(np.max(clean_values)),
                        'p1': float(np.percentile(clean_values, 1)),
                        'p5': float(np.percentile(clean_values, 5)),
                        'p10': float(np.percentile(clean_values, 10)),
                        'p25': float(np.percentile(clean_values, 25)),
                        'p50': float(np.percentile(clean_values, 50)),
                        'p75': float(np.percentile(clean_values, 75)),
                        'p90': float(np.percentile(clean_values, 90)),
                        'p95': float(np.percentile(clean_values, 95)),
                        'p99': float(np.percentile(clean_values, 99)),
                        'skewness': float(self._calculate_skewness(clean_values)),
                        'kurtosis': float(self._calculate_kurtosis(clean_values)),
                        'var_at_risk_5': float(np.percentile(clean_values, 5)),
                        'cvar_at_risk_5': float(np.mean(clean_values[clean_values <= np.percentile(clean_values, 5)]))
                    }

                    # Calculate probability of positive values for financial metrics
                    if key in ['NPV_equity', 'NPV_project']:
                        risk_metrics[f'{key}_prob_positive'] = float(np.mean(clean_values > 0))
                        risk_metrics[f'{key}_prob_negative'] = float(np.mean(clean_values < 0))

                    # Calculate probability of meeting thresholds
                    if key in ['IRR_equity', 'IRR_project']:
                        risk_metrics[f'{key}_prob_above_12pct'] = float(np.mean(clean_values > 0.12))
                        risk_metrics[f'{key}_prob_above_15pct'] = float(np.mean(clean_values > 0.15))

                    if key == 'Min_DSCR':
                        risk_metrics[f'{key}_prob_above_125'] = float(np.mean(clean_values > 1.25))
                        risk_metrics[f'{key}_prob_below_1'] = float(np.mean(clean_values < 1.0))

                else:
                    statistics[key] = {k: 0 for k in ['mean', 'std', 'min', 'max', 'p1', 'p5', 'p10', 'p25', 'p50', 'p75', 'p90', 'p95', 'p99', 'skewness', 'kurtosis', 'var_at_risk_5', 'cvar_at_risk_5']}

            # Calculate correlation matrix of results
            correlation_results = self._calculate_result_correlations(results)

            success_rate = len([v for v in results['IRR_equity'] if not np.isnan(v) and np.isfinite(v)]) / n_simulations

            return {
                'results': results,
                'statistics': statistics,
                'risk_metrics': risk_metrics,
                'correlation_matrix': correlation_results,
                'input_correlation_matrix': correlation_matrix.tolist(),
                'input_parameters': param_names,
                'n_simulations': n_simulations,
                'success_rate': success_rate
            }

        except Exception as e:
            self.logger.error(f"Enhanced Monte Carlo simulation failed: {e}")
            # Return empty results as fallback
            return {
                'results': {key: [] for key in ['IRR_equity', 'IRR_project', 'NPV_equity', 'NPV_project', 'LCOE_eur_kwh', 'Min_DSCR', 'Payback_years']},
                'statistics': {},
                'risk_metrics': {},
                'correlation_matrix': {},
                'input_correlation_matrix': [],
                'input_parameters': [],
                'n_simulations': 0,
                'success_rate': 0
            }

    def _calculate_skewness(self, values):
        """Calculate skewness of a distribution."""
        try:
            n = len(values)
            if n < 3:
                return 0
            mean = np.mean(values)
            std = np.std(values)
            if std == 0:
                return 0
            skew = np.sum(((values - mean) / std) ** 3) / n
            return skew
        except:
            return 0

    def _calculate_kurtosis(self, values):
        """Calculate kurtosis of a distribution."""
        try:
            n = len(values)
            if n < 4:
                return 0
            mean = np.mean(values)
            std = np.std(values)
            if std == 0:
                return 0
            kurt = np.sum(((values - mean) / std) ** 4) / n - 3  # Excess kurtosis
            return kurt
        except:
            return 0

    def _calculate_result_correlations(self, results):
        """Calculate correlation matrix of Monte Carlo results."""
        try:
            import pandas as pd

            # Convert results to DataFrame
            df = pd.DataFrame(results)

            # Remove NaN values
            df = df.dropna()

            if len(df) < 10:  # Need sufficient data points
                return {}

            # Calculate correlation matrix
            corr_matrix = df.corr()

            # Convert to dictionary format
            correlation_dict = {}
            for i, col1 in enumerate(corr_matrix.columns):
                correlation_dict[col1] = {}
                for j, col2 in enumerate(corr_matrix.columns):
                    correlation_dict[col1][col2] = float(corr_matrix.iloc[i, j])

            return correlation_dict

        except Exception as e:
            self.logger.warning(f"Failed to calculate result correlations: {e}")
            return {}

    def _validate_simest_compliance(self, assumptions: EnhancedProjectAssumptions) -> Dict[str, Any]:
        """Validate SIMEST program compliance and provide warnings/recommendations."""
        validation_result = {
            'is_compliant': True,
            'errors': [],
            'warnings': [],
            'recommendations': []
        }
        
        try:
            # Check if SIMEST is being used
            if assumptions.simest_total_facility_meur <= 0:
                # Check if project could benefit from SIMEST
                if self._could_benefit_from_simest(assumptions):
                    validation_result['recommendations'].append(
                        "This African project by an Italian company could benefit from SIMEST financing. "
                        "Consider applying for the SIMEST 'Strengthening African Markets' program."
                    )
                return validation_result
            
            # Validate SIMEST program parameters
            try:
                # Check eligibility using project assumptions validation
                eligibility = assumptions._validate_simest_eligibility()
                if not eligibility['is_eligible']:
                    validation_result['is_compliant'] = False
                    validation_result['errors'].extend(eligibility['errors'])
            except Exception as e:
                validation_result['warnings'].append(f"Could not validate SIMEST eligibility: {str(e)}")
            
            # Validate facility amounts
            try:
                facility_validation = assumptions._validate_simest_facility_amounts()
                if facility_validation:
                    validation_result['is_compliant'] = False
                    validation_result['errors'].extend(facility_validation.values())
            except Exception as e:
                validation_result['warnings'].append(f"Could not validate SIMEST facility amounts: {str(e)}")
            
            # Validate grant limits
            try:
                grant_validation = assumptions._validate_simest_grant_limits()
                if grant_validation:
                    validation_result['is_compliant'] = False
                    validation_result['errors'].extend(grant_validation.values())
            except Exception as e:
                validation_result['warnings'].append(f"Could not validate SIMEST grant limits: {str(e)}")
            
            # Check for optimal SIMEST usage
            if assumptions.company_southern_italy:
                expected_grant_pct = 0.20
                validation_result['recommendations'].append(
                    "Southern Italy company detected - eligible for 20% grant percentage in SIMEST program."
                )
            else:
                expected_grant_pct = 0.10
                validation_result['recommendations'].append(
                    "Standard Italian company - eligible for 10% grant percentage in SIMEST program."
                )
            
            # Check if grant percentage is being utilized optimally
            actual_grant_component = assumptions.calculate_simest_grant_component()
            max_possible_grant = assumptions.capex_meur * expected_grant_pct
            
            if actual_grant_component < max_possible_grant * 0.8:  # Less than 80% of maximum
                validation_result['recommendations'].append(
                    f"SIMEST grant component ({actual_grant_component:.2f} MEUR) could be increased up to "
                    f"{max_possible_grant:.2f} MEUR ({expected_grant_pct*100:.0f}% of CAPEX)."
                )
            
            # Validate soft loan terms
            simest_terms = assumptions.get_simest_soft_loan_terms()
            if simest_terms['interest_rate'] > 0.01:  # Above 1%
                validation_result['warnings'].append(
                    f"SIMEST soft loan interest rate ({simest_terms['interest_rate']*100:.3f}%) "
                    "seems high for the preferential program rate (typically 0.511%)."
                )
            
        except Exception as e:
            validation_result['warnings'].append(f"SIMEST compliance validation failed: {str(e)}")
        
        return validation_result
    
    def _could_benefit_from_simest(self, assumptions: EnhancedProjectAssumptions) -> bool:
        """Check if a project could benefit from SIMEST program."""
        try:
            # Check basic eligibility criteria
            if 'italy' not in assumptions.company_location.lower():
                return False
            
            # Check if project is in Africa (simplified check)
            african_keywords = ['morocco', 'algeria', 'tunisia', 'egypt', 'libya', 'sudan', 'ethiopia', 
                              'kenya', 'uganda', 'tanzania', 'rwanda', 'burundi', 'nigeria', 'ghana', 
                              'south_africa', 'angola', 'mozambique', 'zambia', 'zimbabwe', 'botswana',
                              'africa', 'african']
            
            project_location_lower = assumptions.project_location.lower()
            is_african_project = any(keyword in project_location_lower for keyword in african_keywords)
            
            if not is_african_project:
                return False
            
            # Check if project size is within SIMEST range (0.1 - 50 MEUR)
            if assumptions.capex_meur < 0.1 or assumptions.capex_meur > 50:
                return False
            
            # Check if project could benefit from preferential financing
            if assumptions.debt_ratio > 0.5:  # Projects with significant debt could benefit
                return True
            
            return True
            
        except Exception:
            return False

    def _run_enhanced_scenario_analysis(self, base_assumptions, scenarios, progress_callback=None):
        """Run enhanced scenario analysis with DCF model."""
        try:
            import copy

            results = {}
            scenario_definitions = self._get_scenario_definitions()

            total_scenarios = len(scenarios)
            for i, scenario_name in enumerate(scenarios):
                if progress_callback:
                    progress = 50 + (i / total_scenarios) * 40  # 50-90% range
                    progress_callback(progress, f"Running {scenario_name} scenario...")

                # Get scenario modifications
                scenario_mods = scenario_definitions.get(scenario_name, {})

                # Create modified assumptions
                scenario_assumptions = copy.deepcopy(base_assumptions)

                # Apply scenario modifications
                for param, modifier in scenario_mods.items():
                    if hasattr(scenario_assumptions, param):
                        base_value = getattr(scenario_assumptions, param)
                        if isinstance(modifier, dict):
                            if 'multiply' in modifier:
                                new_value = base_value * modifier['multiply']
                            elif 'add' in modifier:
                                new_value = base_value + modifier['add']
                            else:
                                new_value = modifier.get('value', base_value)
                        else:
                            new_value = modifier
                        setattr(scenario_assumptions, param, new_value)

                try:
                    # Run DCF model with scenario assumptions
                    cashflow = self.dcf_model.build_cashflow(scenario_assumptions)
                    kpis = self.dcf_model.compute_kpis(cashflow, scenario_assumptions)

                    results[scenario_name] = {
                        'kpis': kpis,
                        'assumptions': scenario_assumptions.__dict__,
                        'scenario_modifications': scenario_mods
                    }

                except Exception as e:
                    self.logger.warning(f"Scenario {scenario_name} failed: {e}")
                    results[scenario_name] = {
                        'error': str(e),
                        'kpis': {},
                        'assumptions': {},
                        'scenario_modifications': scenario_mods
                    }

            return {
                'scenarios': results,
                'base_assumptions': base_assumptions.__dict__,
                'analysis_date': datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Enhanced scenario analysis failed: {e}")
            return {
                'scenarios': {},
                'base_assumptions': {},
                'analysis_date': datetime.now().isoformat(),
                'error': str(e)
            }

    def _get_scenario_definitions(self):
        """Get predefined scenario modifications including SIMEST program scenarios."""
        return {
            'Base': {},  # No modifications
            'Optimistic': {
                'production_mwh_year1': {'multiply': 1.1},  # 10% higher production
                'ppa_price_eur_kwh': {'multiply': 1.05},    # 5% higher price
                'capex_meur': {'multiply': 0.95},           # 5% lower capex
                'opex_keuros_year1': {'multiply': 0.9}      # 10% lower opex
            },
            'Pessimistic': {
                'production_mwh_year1': {'multiply': 0.9},  # 10% lower production
                'ppa_price_eur_kwh': {'multiply': 0.95},    # 5% lower price
                'capex_meur': {'multiply': 1.1},            # 10% higher capex
                'opex_keuros_year1': {'multiply': 1.1}      # 10% higher opex
            },
            'High_Grants': {
                'total_grants_meur': {'multiply': 2.0}      # Double the grants
            },
            'No_Grants': {
                'total_grants_meur': {'value': 0.0},        # No grants
                'simest_grant_meur': {'value': 0.0},        # No SIMEST grant
                'simest_soft_loan_meur': {'value': 0.0}     # No SIMEST soft loan
            },
            # SIMEST-specific scenarios
            'SIMEST_Grant_Only': {
                'simest_soft_loan_meur': {'value': 0.0}     # Only grant component, no soft loan
            },
            'SIMEST_Full_Program': {
                'simest_grant_meur': {'multiply': 1.2},     # 20% higher grant (Southern Italy scenario)
                'simest_soft_loan_meur': {'multiply': 1.1}  # 10% higher soft loan availability
            },
            'SIMEST_Reduced_Benefits': {
                'simest_grant_meur': {'multiply': 0.8},     # 20% lower grant
                'simest_interest_rate': {'multiply': 1.5}   # 50% higher interest rate (less preferential)
            },
            'No_SIMEST': {
                'simest_grant_meur': {'value': 0.0},        # No SIMEST grant
                'simest_soft_loan_meur': {'value': 0.0}     # No SIMEST soft loan
            }
        }

    def get_current_results(self) -> Optional[Dict[str, Any]]:
        """Get current financial model results."""
        return self._current_results
    
    def get_sensitivity_results(self) -> Optional[pd.DataFrame]:
        """Get sensitivity analysis results."""
        return self._sensitivity_results
    
    def get_monte_carlo_results(self) -> Optional[Dict[str, Any]]:
        """Get Monte Carlo simulation results."""
        return self._monte_carlo_results

    def get_scenario_results(self) -> Optional[Dict[str, Any]]:
        """Get scenario analysis results."""
        return self._scenario_results
    
    def get_scenario_results(self) -> Optional[Dict[str, Any]]:
        """Get scenario analysis results."""
        return self._scenario_results
    
    def has_results(self) -> bool:
        """Check if financial model has been run."""
        return self._current_results is not None
    
    def clear_results(self):
        """Clear all cached results."""
        self._current_results = None
        self._sensitivity_results = None
        self._monte_carlo_results = None
        self._scenario_results = None
        self.logger.info("All results cleared")
    
    def get_kpi_summary(self) -> Optional[Dict[str, Any]]:
        """Get KPI summary for display including SIMEST-specific metrics."""
        if not self._current_results:
            return None
        
        kpis = self._current_results.get('kpis', {})
        return {
            'irr_project': kpis.get('IRR_project', 0),
            'irr_equity': kpis.get('IRR_equity', 0),
            'npv_project': kpis.get('NPV_project', 0),
            'npv_equity': kpis.get('NPV_equity', 0),
            'lcoe': kpis.get('LCOE_eur_kwh', 0),
            'min_dscr': kpis.get('Min_DSCR', 0),
            'avg_dscr': kpis.get('Avg_DSCR', 0),
            'payback_years': kpis.get('Payback_years', 0),
            'grant_percentage': kpis.get('Grant_percentage', 0),
            # SIMEST-specific metrics
            'simest_grant_percentage': kpis.get('SIMEST_grant_percentage', 0),
            'simest_financing_benefit_npv': kpis.get('SIMEST_financing_benefit_npv', 0),
            'regular_debt_meur': kpis.get('Regular_debt_meur', 0),
            'simest_soft_loan_meur': kpis.get('SIMEST_soft_loan_meur', 0),
            'total_debt_meur': kpis.get('Total_debt_meur', 0)
        }
    
    def get_cashflow_summary(self) -> Optional[Dict[str, Any]]:
        """Get cashflow summary for display with SIMEST split structure."""
        if not self._current_results:
            return None
        
        cashflow = self._current_results.get('cashflow')
        if cashflow is None:
            return None
        
        if isinstance(cashflow, dict):
            df = pd.DataFrame(cashflow)
        else:
            df = cashflow
        
        # Handle different OPEX column names from enhanced DCF model
        opex_column = 'Total_OPEX' if 'Total_OPEX' in df.columns else 'OPEX'
        
        return {
            'total_revenue': df['Revenue'].sum() if 'Revenue' in df.columns else 0,
            'total_opex': abs(df[opex_column].sum()) if opex_column in df.columns else 0,
            'total_capex': abs(df['Capex'].sum()) if 'Capex' in df.columns else 0,
            'total_grants': df['Grants'].sum() if 'Grants' in df.columns else 0,
            'project_life': len(df) - 1,  # Excluding year 0
            'first_year_cf': df.loc[1, 'Equity_CF'] if 1 in df.index and 'Equity_CF' in df.columns else 0,
            'last_year_cf': df.iloc[-1]['Equity_CF'] if len(df) > 0 and 'Equity_CF' in df.columns else 0,
            # SIMEST-specific summary information
            'simest_grant_total': self._current_results.get('dcf_assumptions', {}).get('simest_grant_meur', 0),
            'simest_soft_loan_total': self._current_results.get('dcf_assumptions', {}).get('simest_soft_loan_meur', 0),
            'simest_financing_benefit': self._current_results.get('kpis', {}).get('SIMEST_financing_benefit_npv', 0)
        }
