"""
Chart Factory
=============

Factory class for creating various chart components.
"""

import flet as ft
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Set backend for thread-safe operation
import matplotlib.patches as patches
import numpy as np
import io
import base64
from pathlib import Path
import logging
from config.export_config import ExportConfig
from datetime import datetime
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio
from matplotlib.patches import Rectangle
from matplotlib.colors import LinearSegmentedColormap
import warnings
warnings.filterwarnings('ignore')

# Import benchmarks service
try:
    from services.industry_benchmarks_service import IndustryBenchmarksService, TechnologyType, RegionType
except ImportError:
    # Fallback if service not available
    IndustryBenchmarksService = None
    TechnologyType = None
    RegionType = None

# Set professional styling
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")


class ChartFactory:
    """Factory for creating professional chart components with advanced styling."""

    def __init__(self, export_config: Optional[ExportConfig] = None):
        # Initialize export configuration
        self.export_config = export_config or ExportConfig()

        # Get color palette from configuration
        self.default_colors = self.export_config.get_color_palette()
        self.logger = logging.getLogger(__name__)

        # Initialize benchmarks service
        self.benchmarks_service = IndustryBenchmarksService() if IndustryBenchmarksService else None

        # Get chart export settings from configuration
        chart_settings = self.export_config.get_chart_settings()
        quality_settings = self.export_config.get_export_quality_settings()

        self.export_settings = {
            'dpi': quality_settings['dpi'],
            'format': chart_settings['format'].lower(),
            'bbox_inches': 'tight',
            'facecolor': chart_settings['background_color'],
            'edgecolor': 'none',
            'transparent': chart_settings['transparent_background'],
            'optimize': quality_settings.get('optimize', False),
            'progressive': quality_settings.get('progressive', True)
        }

        # Professional color schemes
        self.professional_colors = {
            'primary_palette': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b'],
            'financial_palette': ['#2E8B57', '#DC143C', '#4169E1', '#FF8C00', '#9932CC', '#8B4513'],
            'risk_palette': ['#228B22', '#FFD700', '#FF4500', '#DC143C', '#8B0000'],
            'corporate_palette': ['#003366', '#0066CC', '#66B2FF', '#B3D9FF', '#E6F3FF'],
            'seaborn_palette': sns.color_palette("husl", 10).as_hex(),
            'success_palette': ['#2E8B57', '#32CD32', '#90EE90', '#98FB98', '#F0FFF0'],
            'danger_palette': ['#8B0000', '#DC143C', '#FF6347', '#FFA07A', '#FFE4E1'],
            'warning_palette': ['#FF8C00', '#FFD700', '#FFFF00', '#FFFFE0', '#FFFACD'],
            'secondary_palette': ['#4682B4', '#87CEEB', '#B0E0E6', '#E0F6FF', '#F0F8FF'],
            'text_primary': '#2C3E50',
            'text_secondary': '#7F8C8D',
            'background_light': '#FAFAFA',
            'background_dark': '#34495E'
        }

        # Professional styling defaults with fallback fonts
        available_fonts = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
        font_family = 'DejaVu Sans'  # Default fallback

        # Try to find a suitable font
        try:
            import matplotlib.font_manager as fm
            system_fonts = {f.name for f in fm.fontManager.ttflist}
            for font in available_fonts:
                if font in system_fonts or font == 'sans-serif':
                    font_family = font
                    break
        except Exception:
            # If font detection fails, use default
            pass

        self.professional_style = {
            'figure_size': (12, 8),
            'title_size': 16,
            'label_size': 12,
            'tick_size': 10,
            'legend_size': 11,
            'line_width': 2.5,
            'marker_size': 8,
            'grid_alpha': 0.3,
            'bar_alpha': 0.8,
            'font_family': font_family,
            'title_weight': 'bold',
            'spine_width': 1.2
        }

        # Configure matplotlib for professional output
        plt.rcParams.update({
            'font.family': self.professional_style['font_family'],
            'font.size': self.professional_style['tick_size'],
            'axes.titlesize': self.professional_style['title_size'],
            'axes.labelsize': self.professional_style['label_size'],
            'xtick.labelsize': self.professional_style['tick_size'],
            'ytick.labelsize': self.professional_style['tick_size'],
            'legend.fontsize': self.professional_style['legend_size'],
            'figure.titlesize': self.professional_style['title_size'],
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.grid': True,
            'grid.alpha': self.professional_style['grid_alpha']
        })

    def _save_chart_to_file(self, fig, filepath: Path, title: str = "") -> bool:
        """Save matplotlib figure to file."""
        try:
            filepath.parent.mkdir(parents=True, exist_ok=True)
            fig.savefig(
                filepath,
                **self.export_settings
            )
            self.logger.info(f"Chart saved: {filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to save chart: {e}")
            return False

    def _get_chart_bytes(self, fig) -> bytes:
        """Convert matplotlib figure to bytes."""
        try:
            buffer = io.BytesIO()
            fig.savefig(buffer, format='png', **self.export_settings)
            buffer.seek(0)
            return buffer.getvalue()
        except Exception as e:
            self.logger.error(f"Failed to convert chart to bytes: {e}")
            return b''

    def _create_ui_component(self, fig, width: int = 800, height: int = 600) -> ft.Container:
        """Create Flet UI component from matplotlib figure."""
        try:
            chart_bytes = self._get_chart_bytes(fig)
            if chart_bytes:
                return ft.Container(
                    content=ft.Image(
                        src_base64=base64.b64encode(chart_bytes).decode(),
                        width=width,
                        height=height,
                        fit=ft.ImageFit.CONTAIN
                    ),
                    width=width,
                    height=height,
                    alignment=ft.alignment.center
                )
            else:
                return ft.Container(
                    content=ft.Text("Chart generation failed"),
                    width=width,
                    height=height
                )
        except Exception as e:
            self.logger.error(f"Failed to create UI component: {e}")
            return ft.Container(
                content=ft.Text(f"Chart error: {str(e)}"),
                width=width,
                height=height
            )

    def _get_individual_chart_path(self, base_path: Path, chart_name: str) -> Optional[Path]:
        """Get individual chart file path."""
        try:
            if base_path.is_dir():
                return base_path / f"{chart_name}.png"
            else:
                # If base_path is a file, create a sibling file
                return base_path.parent / f"{base_path.stem}_{chart_name}.png"
        except Exception as e:
            self.logger.error(f"Failed to create chart path: {e}")
            return None

    def create_financial_structure_chart(self,
                                       assumptions: Dict[str, Any] = None,
                                       title: str = "Project Financial Structure",
                                       save_path: Optional[Path] = None,
                                       financial_results: Dict[str, Any] = None) -> Tuple[ft.Container, bytes]:
        """Create pie chart showing financial structure breakdown with real data."""
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))

        # Try to use real financial data first
        if financial_results and 'kpis' in financial_results:
            kpis = financial_results['kpis']
            assumptions_data = financial_results.get('assumptions', assumptions or {})

            # Extract real financial data
            capex_meur = kpis.get('Total_capex', assumptions_data.get('capex_meur', 100)) / 1e6
            equity_percentage = 1 - assumptions_data.get('debt_ratio', 0.75)
            total_debt_meur = capex_meur * assumptions_data.get('debt_ratio', 0.75)

            self.logger.info("Using real financial data for financial structure chart")
        else:
            # Fallback to provided assumptions or defaults
            assumptions_data = assumptions or {}
            capex_meur = assumptions_data.get('capex_meur', 100)
            equity_percentage = assumptions_data.get('equity_percentage', 1 - assumptions_data.get('debt_ratio', 0.75))
            total_debt_meur = capex_meur * assumptions_data.get('debt_ratio', 0.75)

            self.logger.warning("Using fallback assumptions for financial structure chart")

        # Extract SIMEST split values
        simest_total = assumptions_data.get('simest_total_facility_meur', 0.0)
        simest_grant = assumptions_data.get('simest_grant_meur', 0.0)
        simest_soft = assumptions_data.get('simest_soft_loan_meur', 0.0)

        # Other grants
        grant_morocco = assumptions_data.get('grant_meur_masen', 0.0)
        grant_connection = assumptions_data.get('grant_meur_connection', 0.0)
        grant_cri = assumptions_data.get('grant_meur_cri', 0.0)
        other_grants = grant_morocco + grant_connection + grant_cri

        # Equity and debt split
        equity_meur = capex_meur * equity_percentage
        commercial_debt = max(total_debt_meur - simest_soft, 0.0)

        # Prepare slices
        labels = []
        sizes = []
        colors = []

        # Equity slice
        if equity_meur > 0:
            labels.append(f'Equity\n€{equity_meur:.1f}M\n({equity_meur/capex_meur*100:.1f}%)')
            sizes.append(equity_meur)
            colors.append(self.professional_colors['primary_palette'][2])

        # Commercial Debt slice
        if commercial_debt > 0:
            labels.append(f'Commercial Debt\n€{commercial_debt:.1f}M\n({commercial_debt/capex_meur*100:.1f}%)')
            sizes.append(commercial_debt)
            colors.append(self.professional_colors['secondary_palette'][2])

        # SIMEST Soft Loan slice
        if simest_soft > 0:
            labels.append(f'SIMEST Soft Loan\n€{simest_soft:.1f}M\n({simest_soft/capex_meur*100:.1f}%)')
            sizes.append(simest_soft)
            colors.append(self.professional_colors['financial_palette'][5])

        # SIMEST Grant slice
        if simest_grant > 0:
            labels.append(f'SIMEST Grant\n€{simest_grant:.1f}M\n({simest_grant/capex_meur*100:.1f}%)')
            sizes.append(simest_grant)
            colors.append('#009246')  # Italian green

        # Other Grants slice
        if other_grants > 0:
            labels.append(f'Other Grants\n€{other_grants:.1f}M\n({other_grants/capex_meur*100:.1f}%)')
            sizes.append(other_grants)
            colors.append(self.professional_colors['success_palette'][1])

        # Create pie chart
        wedges, texts, autotexts = ax.pie(
            sizes, labels=labels, colors=colors,
            autopct='%1.1f%%', startangle=90, pctdistance=0.85, explode=[0.03]*len(sizes)
        )

        # Beautify text
        for text in texts:
            text.set_fontsize(11)
            text.set_weight('bold')
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontsize(12)
            autotext.set_weight('bold')

        ax.set_title(title, fontsize=16, fontweight='bold')

        # Save if required
        if save_path:
            self._save_chart_to_file(fig, save_path, title)

        chart_bytes = self._get_chart_bytes(fig)
        ui_component = self._create_ui_component(fig, width=900, height=500)
        plt.close(fig)

        return ui_component, chart_bytes

    def create_lcoe_incentive_waterfall(self, 
                                      baseline_lcoe: float,
                                      incentive_impacts: Dict[str, float],
                                      title: str = "LCOE Impact Analysis: Incentives Breakdown",
                                      save_path: Optional[Path] = None) -> Dict[str, Any]:
        """Create LCOE waterfall chart showing impact of each incentive type."""
        fig, ax = plt.subplots(figsize=(14, 8))

        # Prepare waterfall categories and values
        categories = ['Baseline LCOE']
        values = [baseline_lcoe]
        colors = [self.professional_colors['danger_palette'][1]]  # Red baseline

        incentive_names = {
            'simest_grant': 'SIMEST Grant Impact',
            'simest_soft_loan': 'SIMEST Financing Benefit',
            'masen': 'MASEN Commercial\nIncentives',
            'iresen': 'IRESEN R&D\n(Non-Commercial)',
            'connection': 'Grid Connection\nSupport',
            'cri': 'CRI Regional\nSupport'
        }

        running = baseline_lcoe
        for key, impact in incentive_impacts.items():
            if impact == 0:
                continue
            name = incentive_names.get(key, key.replace('_', ' ').title())
            # For reductions, use negative impact
            reduction = -abs(impact)
            running += reduction
            categories.append(name)
            values.append(reduction)
            if key == 'simest_grant':
                colors.append('#009246')  # Italian green
            elif key == 'simest_soft_loan':
                colors.append(self.professional_colors['financial_palette'][5])  # Brown
            else:
                colors.append(self.professional_colors['success_palette'][1])  # Standard green

        # Final
        categories.append('Final LCOE')
        values.append(running)
        colors.append(self.professional_colors['primary_palette'][0])

        # Plot bars
        cum = 0
        for i, (cat, val, col) in enumerate(zip(categories, values, colors)):
            if i == 0:
                ax.bar(i, val, color=col, alpha=0.8)
                cum = val
            else:
                if i == len(categories) - 1:
                    ax.bar(i, val, color=col, alpha=0.8)
                else:
                    ax.bar(i, val, bottom=cum, color=col, alpha=0.8)
                    cum += val
                # Connect
                if i > 0:
                    ax.plot([i-1 + 0.5, i - 0.5], [cum - val, cum - val], 'k--', alpha=0.5)
            # Label
            label_y = val/2 + (cum - val if i not in [0, len(categories)-1] else 0)
            ax.text(i, label_y, f'{abs(val):.2f}', ha='center', va='center', fontsize=10, fontweight='bold')

        ax.set_xticks(range(len(categories)))
        ax.set_xticklabels(categories, rotation=45, ha='right')
        ax.set_ylabel('LCOE (€/MWh)')
        ax.set_title(title, fontsize=14, fontweight='bold')
        ax.grid(True, axis='y', alpha=0.3)

        # Summary
        total_reduction = baseline_lcoe - running
        percent_red = total_reduction / baseline_lcoe * 100 if baseline_lcoe else 0
        summary = f'Total Reduction: {total_reduction:.2f} €/MWh ({percent_red:.1f}%)'
        ax.text(0.02, 0.95, summary, transform=ax.transAxes, fontsize=11,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.5),
                verticalalignment='top')

        plt.tight_layout()

        if save_path:
            chart_path = self._get_individual_chart_path(save_path, "lcoe_incentive_waterfall")
            if chart_path:
                self._save_chart_to_file(fig, chart_path, title)

        chart_bytes = self._get_chart_bytes(fig)
        ui_component = self._create_ui_component(fig, width=900, height=600)
        plt.close(fig)

        return {
            'title': title,
            'bytes': chart_bytes,
            'path': chart_path if save_path else None,
            'ui_component': ui_component
        }