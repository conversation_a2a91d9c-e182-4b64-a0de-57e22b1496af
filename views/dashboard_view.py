"""
Dashboard View
==============

Enhanced dashboard view with comprehensive financial analysis visualization.
"""

import flet as ft
from typing import Dict, Any, Optional

from .base_view import BaseView
from components.charts.kpi_charts import KPICharts
from components.charts.cashflow_charts import CashflowCharts
from components.widgets.kpi_card import KPICard
from components.widgets.enhanced_features_panel import EnhancedFeaturesPanel, MLInsightsWidget
from services.model_reliability_service import ModelReliabilityService
from services.enhanced_integration_service import get_integration_service
from services.config_service import ConfigurationService

# Enhanced UI/UX components
from components.ui.enhanced_loading_system import (
    create_skeleton, SkeletonType, create_loading_spinner,
    LoadingVariant, ComponentSize as LoadingSize
)
from components.ui.accessibility_system import (
    make_accessible_button, make_accessible_card,
    ScreenReaderSupport, get_accessibility_manager
)
from components.ui.modern_theme_system import ComponentSize


class DashboardView(BaseView):
    """Enhanced dashboard view for financial analysis."""

    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.financial_results: Optional[Dict[str, Any]] = None
        self.ml_predictions: Optional[Dict[str, Any]] = None
        self.charts_3d: Optional[Dict[str, str]] = None
        self.kpi_charts = KPICharts()
        self.cashflow_charts = CashflowCharts()
        self.reliability_service = ModelReliabilityService()
        self.reliability_metrics: Optional[Dict[str, Any]] = None

        # Enhanced features
        self.integration_service = get_integration_service()
        self.enhanced_features_panel = EnhancedFeaturesPanel(
            on_feature_toggle=self._handle_feature_toggle,
            on_action=self._handle_feature_action
        )
        self.ml_insights_widget = MLInsightsWidget()

    def build_content(self) -> ft.Control:
        """Build the dashboard view content with enhanced loading and accessibility."""

        if not self.financial_results:
            # Show enhanced loading state with skeleton screens
            return self._create_enhanced_empty_state()

        # Enhanced Header with 2025 branding
        header = self.create_section_header(
            "Enhanced Financial Analytics Dashboard • 2025",
            "Comprehensive DCF analysis with advanced reliability metrics and trend analysis"
        )

        # Enhanced Features Panel (NEW)
        features_panel = self.enhanced_features_panel.build()
        self._update_features_status()

        # Enhanced KPI Summary Cards with DCF metrics
        kpi_summary = self._create_enhanced_kpi_summary()

        # ML Insights Widget (NEW)
        ml_insights = self._create_ml_insights_section()

        # Model Reliability Assessment
        reliability_assessment = self._create_reliability_assessment()

        # Financial Summary with DCF breakdown
        financial_summary = self._create_enhanced_financial_summary()

        # Advanced Charts Grid with trend analysis
        charts_grid = self._create_enhanced_charts_grid()

        # DCF Analysis Section
        dcf_analysis = self._create_dcf_analysis()

        # Grant Analysis
        grant_analysis = self._create_grant_analysis()

        # Enhanced Risk Analysis with 2025 standards
        risk_analysis = self._create_enhanced_risk_analysis()

        # Factor Analysis and Trends
        factor_analysis = self._create_factor_analysis()

        # 3D Charts Section (NEW)
        charts_3d_section = self._create_3d_charts_section()

        # Create main scrollable container with proper configuration
        main_content = ft.Column([
            header,
            features_panel,  # NEW: Enhanced features control panel
            ml_insights,     # NEW: ML insights section
            charts_3d_section,  # NEW: 3D charts section
            kpi_summary,
            reliability_assessment,
            financial_summary,
            charts_grid,
            dcf_analysis,
            grant_analysis,
            risk_analysis,
            factor_analysis,
            # Add some bottom padding for better scroll experience
            ft.Container(height=50)
        ], spacing=15, tight=True)

        # Wrap in a scrollable container with proper configuration
        return ft.Column(
            [main_content],
            expand=True,
            scroll=ft.ScrollMode.ALWAYS,
            spacing=0,
            tight=True,
            on_scroll=self._handle_scroll_event
        )

    def _get_fallback_financial_data(self) -> Dict[str, Any]:
        """Get fallback financial data when real data is not available."""
        return {
            'kpis': {
                'IRR_project': 0.10,  # 10%
                'IRR_equity': 0.15,   # 15%
                'NPV_project': 1000000,  # 1M EUR
                'NPV_equity': 500000,    # 500k EUR
                'LCOE_eur_kwh': 0.045,   # 4.5 c€/kWh
                'Payback_years': 8.5,    # 8.5 years
                'Terminal_value': 2000000,  # 2M EUR
                'Min_DSCR': 1.25,
                'Avg_DSCR': 1.40
            },
            'cashflow': {},
            'assumptions': {
                'capex_meur': 8.5,
                'opex_keuros_year1': 180,
                'production_mwh_year1': 18000,
                'ppa_price_eur_kwh': 0.045,
                'project_life_years': 25,
                'simest_total_facility_meur': 0.7,
                'company_southern_italy': False,
                'grant_meur_masen': 1.0,
                'grant_meur_connection': 0.3,
                'grant_meur_cri': 0.4
            },
            'is_fallback': True
        }

    def _create_enhanced_kpi_summary(self) -> ft.Container:
        """Create enhanced KPI summary cards with DCF metrics."""
        # CRITICAL FIX: Only use fallback as last resort
        if not self.financial_results or not isinstance(self.financial_results, dict):
            self.logger.error("CRITICAL: No financial results available - this indicates a data flow problem!")
            self.financial_results = self._get_fallback_financial_data()

        kpis = self.financial_results.get('kpis', {})

        # IMPROVED: More lenient KPI validation - check for actual values, not just empty dict
        if not kpis or not any(isinstance(v, (int, float)) and v != 0 for v in kpis.values()):
            self.logger.warning(f"KPIs invalid or all zero: {kpis} - using fallback data")
            fallback_data = self._get_fallback_financial_data()
            kpis = fallback_data['kpis']
        else:
            self.logger.info(f"SUCCESS: Using real KPI data with {len(kpis)} metrics")

        # Debug: Log available KPIs with better detail
        sample_kpis = {k: v for k, v in list(kpis.items())[:3]}
        self.logger.info(f"DEBUG: Creating KPI summary with sample KPIs: {sample_kpis}")

        # Enhanced KPI cards with additional DCF metrics and fallback values
        kpi_cards = ft.Row([
            KPICard(
                title="Project IRR",
                value=f"{kpis.get('IRR_project', 0):.1%}",
                color=ft.Colors.GREEN,
                icon=ft.Icons.TRENDING_UP,
                target_value=12.0,
                current_value=(kpis.get('IRR_project', 0) * 100)
            ).build(),
            KPICard(
                title="Equity IRR",
                value=f"{kpis.get('IRR_equity', 0):.1%}",
                color=ft.Colors.BLUE,
                icon=ft.Icons.ACCOUNT_BALANCE,
                target_value=15.0,
                current_value=(kpis.get('IRR_equity', 0) * 100)
            ).build(),
            KPICard(
                title="NPV Project",
                value=self.format_currency(kpis.get('NPV_project', 0)),
                color=ft.Colors.PURPLE,
                icon=ft.Icons.MONETIZATION_ON
            ).build(),
            KPICard(
                title="Terminal Value",
                value=f"€{kpis.get('Terminal_value', 0)/1e6:.1f}M",
                color=ft.Colors.INDIGO,
                icon=ft.Icons.TIMELINE
            ).build(),
            KPICard(
                title="LCOE",
                value=f"{kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh",
                color=ft.Colors.ORANGE,
                icon=ft.Icons.ELECTRIC_BOLT,
                target_value=0.045,
                current_value=kpis.get('LCOE_eur_kwh', 0),
                lower_is_better=True
            ).build(),
            KPICard(
                title="Payback Period",
                value=f"{kpis.get('Payback_years', 0):.1f} years",
                color=ft.Colors.CYAN,
                icon=ft.Icons.SCHEDULE
            ).build()
        ], alignment=ft.MainAxisAlignment.SPACE_AROUND, wrap=True)

        return ft.Container(
            content=kpi_cards,
            padding=20,
            bgcolor=ft.Colors.GREY_50,
            border_radius=12,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )

    def _create_reliability_assessment(self) -> ft.Card:
        """Create model reliability assessment section."""
        if not self.financial_results:
            return ft.Card()

        try:
            # Calculate reliability metrics
            self.reliability_metrics = self.reliability_service.assess_model_reliability(
                self.financial_results,
                self.financial_results.get('assumptions', {}),
                None,
                None
            )
            reliability_report = self.reliability_service.generate_reliability_report(self.reliability_metrics)

            # Create reliability dashboard
            reliability_content = ft.Column([
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Overall Reliability", size=14, color=ft.Colors.GREY_600),
                            ft.Text(f"{reliability_report['overall_reliability_score']:.0f}/100",
                                   size=24, weight=ft.FontWeight.BOLD, color=ft.Colors.BLUE_600),
                            ft.Text(reliability_report['reliability_grade'],
                                   size=12, color=ft.Colors.BLUE_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=150,
                        padding=15,
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=10
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Confidence Level", size=14, color=ft.Colors.GREY_600),
                            ft.Text(reliability_report['confidence_level'].split(' - ')[0],
                                   size=18, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_600),
                            ft.Text("Model Reliability",
                                   size=12, color=ft.Colors.GREEN_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=150,
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=10
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("95% Confidence Interval", size=14, color=ft.Colors.GREY_600),
                            ft.Text(f"{self.reliability_metrics.confidence_interval_95[0]:.1%} - {self.reliability_metrics.confidence_interval_95[1]:.1%}",
                                   size=16, weight=ft.FontWeight.BOLD, color=ft.Colors.PURPLE_600),
                            ft.Text("IRR Equity Range",
                                   size=12, color=ft.Colors.PURPLE_600, weight=ft.FontWeight.W_500)
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        width=180,
                        padding=15,
                        bgcolor=ft.Colors.PURPLE_50,
                        border_radius=10
                    )
                ], alignment=ft.MainAxisAlignment.SPACE_AROUND),
                ft.Container(height=15),
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("✓ Key Strengths", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.GREEN_700),
                            *[ft.Text(f"• {strength}", size=12, color=ft.Colors.GREY_700)
                              for strength in reliability_report['key_strengths'][:3]]
                        ]),
                        expand=1,
                        padding=15,
                        bgcolor=ft.Colors.GREEN_50,
                        border_radius=8
                    ),
                    ft.Container(width=10),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("⚠ Areas for Improvement", size=14, weight=ft.FontWeight.BOLD, color=ft.Colors.ORANGE_700),
                            *[ft.Text(f"• {improvement}", size=12, color=ft.Colors.GREY_700)
                              for improvement in reliability_report['areas_for_improvement'][:3]]
                        ]),
                        expand=1,
                        padding=15,
                        bgcolor=ft.Colors.ORANGE_50,
                        border_radius=8
                    )
                ])
            ])

            return self.create_card(
                "Model Reliability Assessment • 2025 Standards",
                reliability_content,
                icon=ft.Icons.VERIFIED_USER,
                bgcolor=ft.Colors.WHITE
            )

        except Exception as e:
            return self.create_card(
                "Model Reliability Assessment",
                ft.Text(f"Reliability assessment unavailable: {str(e)}", color=ft.Colors.GREY_600),
                icon=ft.Icons.WARNING,
                bgcolor=ft.Colors.GREY_50
            )

    def _create_enhanced_financial_summary(self) -> ft.Card:
        """Create enhanced financial summary section with DCF breakdown."""
        if not self.financial_results:
            self.logger.warning("No financial results available for summary, using fallback data")
            self.financial_results = self._get_fallback_financial_data()

        self.logger.info(f"DEBUG: Financial results keys: {list(self.financial_results.keys())}")
        cashflow_data = self.financial_results.get('cashflow')
        kpis = self.financial_results.get('kpis', {})

        self.logger.info(f"DEBUG: KPIs available: {list(kpis.keys()) if kpis else 'No KPIs'}")
        self.logger.info(f"DEBUG: Cashflow type: {type(cashflow_data)}")

        if cashflow_data is None or (isinstance(cashflow_data, dict) and not cashflow_data):
            self.logger.warning("No cashflow data available, using KPIs for summary")

            assumptions = self.financial_results.get('assumptions', {})
            if not assumptions:
                self.logger.warning("No assumptions available, using fallback assumptions")
                fallback_data = self._get_fallback_financial_data()
                assumptions = fallback_data['assumptions']
                kpis = fallback_data['kpis']

            total_revenue = 0
            total_opex = 0
            total_capex = assumptions.get('capex_meur', 0)
            grant_cri = assumptions.get('grant_meur_cri', 0)
            masen = assumptions.get('grant_meur_masen', 0)
            connection = assumptions.get('grant_meur_connection', 0)
            simest_facility = assumptions.get('simest_total_facility_meur', 0)
            southern = assumptions.get('company_southern_italy', False)
            pct = 0.20 if southern else 0.10
            simest_grant = simest_facility * pct
            total_grants = simest_grant + masen + connection + grant_cri
            total_ebitda = 0
            terminal_value = kpis.get('Terminal_value', 0) / 1e6

            if 'production_mwh_year1' in assumptions and 'ppa_price_eur_kwh' in assumptions:
                annual_revenue = assumptions['production_mwh_year1'] * 1000 * assumptions['ppa_price_eur_kwh'] / 1e6
                total_revenue = annual_revenue * assumptions.get('project_life_years', 25)

            if 'opex_keuros_year1' in assumptions:
                annual_opex = assumptions['opex_keuros_year1'] / 1000
                total_opex = annual_opex * assumptions.get('project_life_years', 25)
        else:
            import pandas as pd
            df = pd.DataFrame(cashflow_data) if isinstance(cashflow_data, dict) else cashflow_data

            self.logger.info(f"DEBUG: Cashflow columns: {list(df.columns)}")
            total_revenue = df['Revenue'].sum() / 1e6 if 'Revenue' in df.columns else 0
            total_opex = abs(df['Total_OPEX'].sum()) / 1e6 if 'Total_OPEX' in df.columns else 0
            total_capex = abs(df['Capex'].sum()) / 1e6 if 'Capex' in df.columns else 0
            total_grants = df['Grants'].sum() / 1e6 if 'Grants' in df.columns else 0
            total_ebitda = df['EBITDA'].sum() / 1e6 if 'EBITDA' in df.columns else 0
            terminal_value = df['Terminal_Value'].sum() / 1e6 if 'Terminal_Value' in df.columns else 0

        summary_cards = ft.Column([
            ft.Row([
                self._create_summary_card(
                    "Total Revenue",
                    f"€{total_revenue:.1f}M",
                    ft.Colors.GREEN,
                    ft.Icons.TRENDING_UP
                ),
                self._create_summary_card(
                    "Total EBITDA",
                    f"€{total_ebitda:.1f}M",
                    ft.Colors.TEAL,
                    ft.Icons.ACCOUNT_BALANCE_WALLET
                ),
                self._create_summary_card(
                    "Total OPEX",
                    f"€{total_opex:.1f}M",
                    ft.Colors.RED,
                    ft.Icons.TRENDING_DOWN
                ),
                self._create_summary_card(
                    "Total CAPEX",
                    f"€{total_capex:.1f}M",
                    ft.Colors.BLUE,
                    ft.Icons.BUILD
                )
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND),

            ft.Container(height=10),

            ft.Row([
                self._create_summary_card(
                    "Total Grants",
                    f"€{total_grants:.1f}M",
                    ft.Colors.PURPLE,
                    ft.Icons.CARD_GIFTCARD
                ),
                self._create_summary_card(
                    "Terminal Value",
                    f"€{terminal_value:.1f}M",
                    ft.Colors.INDIGO,
                    ft.Icons.TIMELINE
                ),
                self._create_summary_card(
                    "EBITDA Margin",
                    f"{(total_ebitda/total_revenue*100):.1f}%" if total_revenue > 0 else "0.0%",
                    ft.Colors.CYAN,
                    ft.Icons.PERCENT
                ),
                self._create_summary_card(
                    "Grant Coverage",
                    f"{(total_grants/total_capex*100):.1f}%" if total_capex > 0 else "0.0%",
                    ft.Colors.ORANGE,
                    ft.Icons.SHIELD
                )
            ], alignment=ft.MainAxisAlignment.SPACE_AROUND)
        ])

        return self.create_card(
            "Enhanced Project Financial Summary • DCF Analysis",
            summary_cards,
            icon=ft.Icons.ACCOUNT_BALANCE_WALLET
        )

    def _create_summary_card(self, title: str, value: str, color: str, icon: str) -> ft.Container:
        """Create a summary metric card."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(icon, color=color, size=30),
                ft.Text(title, size=12, color=ft.Colors.GREY_600),
                ft.Text(value, size=16, weight=ft.FontWeight.BOLD, color=color)
            ], alignment=ft.MainAxisAlignment.CENTER),
            width=120,
            height=100,
            padding=10,
            bgcolor=f"{color}10",
            border_radius=8
        )

    def _create_charts_grid(self) -> ft.Container:
        """Create charts grid layout."""
        if not self.financial_results:
            return ft.Container()

        kpi_gauge_chart = self.kpi_charts.create_kpi_gauge_chart(self.financial_results)
        irr_comparison_chart = self.kpi_charts.create_irr_comparison_chart(self.financial_results)
        cashflow_timeline = self.cashflow_charts.create_cashflow_timeline_chart(self.financial_results)
        dscr_timeline = self.cashflow_charts.create_dscr_timeline_chart(self.financial_results)

        charts_grid = ft.Column([
            ft.Row([
                ft.Container(content=kpi_gauge_chart, expand=1),
                ft.Container(content=irr_comparison_chart, expand=1)
            ]),
            ft.Row([
                ft.Container(content=cashflow_timeline, expand=1),
                ft.Container(content=dscr_timeline, expand=1)
            ])
        ])

        return self.create_card(
            "Financial Analysis Charts",
            charts_grid,
            icon=ft.Icons.ANALYTICS
        )

    def _create_grant_analysis(self) -> ft.Card:
        """Create grant analysis section with split SIMEST structure."""
        assumptions = self.financial_results.get('assumptions', {})

        total_capex = assumptions.get('capex_meur', 1)
        masen = assumptions.get('grant_meur_masen', 0)
        connection = assumptions.get('grant_meur_connection', 0)
        cri = assumptions.get('grant_meur_cri', 0)
        simest_facility = assumptions.get('simest_total_facility_meur', 0)
        southern = assumptions.get('company_southern_italy', False)
        grant_pct = 0.20 if southern else 0.10
        simest_grant = simest_facility * grant_pct
        simest_soft_loan = simest_facility - simest_grant

        # Retrieve soft loan terms from config
        try:
            cs = ConfigurationService()
            terms = cs.get_simest_soft_loan_terms()
            loan_terms = f"{terms['tenor_years']}y tenor, {terms['grace_years']}y grace at {terms['interest_rate']*100:.2f}%"
        except:
            loan_terms = "6y tenor, 2y grace at 0.51%"

        # Create text breakdown
        grant_breakdown = ft.Column([
            self.create_info_row("SIMEST Grant", f"€{simest_grant:.2f}M", ft.Colors.PURPLE),
            self.create_info_row("SIMEST Soft Loan", f"€{simest_soft_loan:.2f}M", ft.Colors.BLUE),
            self.create_info_row("Soft Loan Terms", loan_terms),
            ft.Divider(),
            self.create_info_row("MASEN Grant", f"€{masen:.2f}M"),
            self.create_info_row("Grid Connection Grant", f"€{connection:.2f}M"),
            self.create_info_row("CRI Grant", f"€{cri:.2f}M"),
            ft.Divider(),
            self.create_info_row("Total Grants", f"€{(simest_grant + masen + connection + cri):.2f}M", ft.Colors.GREEN),
            self.create_info_row("Grant % of CAPEX", f"{((simest_grant + masen + connection + cri)/total_capex*100):.1f}%", ft.Colors.GREEN),
            self.create_info_row("Soft Loan % of CAPEX", f"{(simest_soft_loan/total_capex*100):.1f}%", ft.Colors.BLUE)
        ])

        # Financial structure chart if available
        financial_chart = None
        try:
            from components.charts.chart_factory import ChartFactory
            cf = ChartFactory()
            financial_chart, _ = cf.create_financial_structure_chart(
                assumptions=assumptions,
                title="Project Financial Structure & SIMEST Split"
            )
        except Exception as e:
            self.logger.warning(f"Could not create financial structure chart: {e}")

        content = grant_breakdown
        if financial_chart:
            content = ft.Column([
                grant_breakdown,
                ft.Container(height=20),
                ft.Text("Financial Structure Visualization", size=16, weight=ft.FontWeight.BOLD),
                ft.Container(content=financial_chart, height=400)
            ])

        return self.create_card(
            "Grant Analysis & Financial Structure",
            content,
            icon=ft.Icons.CARD_GIFTCARD,
            bgcolor=ft.Colors.GREEN_50
        )

    def _create_risk_analysis(self) -> ft.Card:
        """Create risk analysis section including SIMEST compliance."""
        if not self.financial_results:
            return ft.Card()

        kpis = self.financial_results.get('kpis', {})
        risk_indicators = []

        # IRR Risk
        project_irr = kpis.get('IRR_project', 0)
        if project_irr < 0.10:
            risk_indicators.append(("High Risk", "Project IRR below 10%", ft.Colors.RED))
        elif project_irr < 0.12:
            risk_indicators.append(("Medium Risk", "Project IRR below target 12%", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Project IRR above target", ft.Colors.GREEN))

        # DSCR Risk
        min_dscr = kpis.get('Min_DSCR', 0)
        if min_dscr < 1.20:
            risk_indicators.append(("High Risk", "DSCR below 1.20", ft.Colors.RED))
        elif min_dscr < 1.35:
            risk_indicators.append(("Medium Risk", "DSCR below comfortable level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Strong debt coverage", ft.Colors.GREEN))

        # LCOE Risk
        lcoe = kpis.get('LCOE_eur_kwh', 0)
        if lcoe > 0.055:
            risk_indicators.append(("High Risk", "LCOE above market", ft.Colors.RED))
        elif lcoe > 0.045:
            risk_indicators.append(("Medium Risk", "LCOE above competitive level", ft.Colors.ORANGE))
        else:
            risk_indicators.append(("Low Risk", "Competitive LCOE", ft.Colors.GREEN))

        # SIMEST Program Compliance
        assumptions = self.financial_results.get('assumptions', {})
        simest_facility = assumptions.get('simest_total_facility_meur', 0)
        if simest_facility > 0:
            risk_indicators.append(("SIMEST Program", "SIMEST financing applied", ft.Colors.GREEN))
        else:
            risk_indicators.append(("SIMEST Program", "No SIMEST financing", ft.Colors.ORANGE))

        risk_content = ft.Column([
            ft.Row([
                ft.Icon(ft.Icons.WARNING, color=col),
                ft.Text(label, weight=ft.FontWeight.BOLD, color=col),
                ft.Text(desc, expand=True)
            ]) for label, desc, col in risk_indicators
        ])

        return self.create_card(
            "Risk Assessment",
            risk_content,
            icon=ft.Icons.SECURITY,
            bgcolor=ft.Colors.ORANGE_50
        )

    # ... rest of the methods unchanged ...